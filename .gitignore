# UniApp 构建输出目录
unpackage/

# Node.js 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器和IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 微信开发者工具生成的文件
project.config.json
project.private.config.json

# HBuilderX 生成的文件
.hbuilderx/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 其他常见忽略文件
*.orig
.nyc_output/
coverage/