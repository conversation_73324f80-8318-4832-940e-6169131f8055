import request from "@/utils/request.js"

/* 
 * 已入库列表
 */
export function ListIn(data, loading = true) {
	return request({
		url: 'v2/listIn',
		method: 'GET',
		data,
		loading
	})
}

/* 
 * 扫描入库
 */
export function EntryScanning(data, loading = true) {
	return request({
		url: 'v2/entryScanning',
		method: 'POST',
		data,
		loading
	})
}

/* 
 * 获取下拉列表于历史姓名列表
 */
export function GetPutName(data, loading = true) {
	return request({
		url: 'v2/getPutName',
		method: 'POST',
		data,
		loading
	})
}

/* 
 * 获取唯一码
 */
export function GetOnlyCodeByName(data, loading = true) {
	return request({
		url: 'v2/getOnlyCodeByName',
		method: 'POST',
		data,
		loading
	})
}

