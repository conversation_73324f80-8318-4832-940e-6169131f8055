import request from "@/utils/request.js"

/* 
 * 获取已出库列表
 */
export function ListOut(data, loading = true) {
	return request({
		url: 'v2/listOut',
		method: 'GET',
		data,
		loading
	})
}

/* 
 * 出库订单详情
 */
export function OutInfo(id, loading = true) {
	return request({
		url: `v2/outInfo/${id}`,
		method: 'GET',
		loading
	})
}

/* 
 * 扫描出库
 */
export function OutPutOrder(data, loading = true) {
	return request({
		url: 'v2/outPutOrder',
		method: 'POST',
		data,
		loading
	})
}

/* 
 * 修改出库信息
 */
export function UpdateOut(data, loading = true) {
	return request({
		url: 'v2/updateOut',
		method: 'POST',
		data,
		loading
	})
}

/* 
 * 查询 近似单号
 */
export function GetLikeInOrder(data, loading = true) {
	return request({
		url: 'v2/getLikeInOrder',
		method: 'POST',
		data,
		loading
	})
}

/* 
 * 查询快递单号
 */
export function GetInOrder(data, loading = true) {
	return request({
		url: 'v2/getInOrder',
		method: 'POST',
		data,
		loading
	})
}

