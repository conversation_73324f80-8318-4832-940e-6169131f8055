import request from "@/utils/request.js"

export function WxUserLogin(data, loading = true) {
	return request({
		url: 'v2/wxUserLogin',
		method: 'POST',
		data,
		loading
	})
}

export function UpdateUserInfo(data, loading = true) {
	return request({
		url: 'v2/updateUserInfo',
		method: 'POST',
		data,
		loading
	})
}

export function UpdateFlag(data, loading = true) {
	return request({
		url: 'v2/updateFlag',
		method: 'POST',
		data,
		loading
	})
}

export function GetJnUser(loading = true) {
	return request({
		url: 'v2/getJnUser',
		method: 'POST',
		loading
	})
}

export function GetOutPutName(loading = true) {
	return request({
		url: 'v2/getOutPutName',
		method: 'POST',
		loading
	})
}

export function GetBusiness(loading = true) {
	return request({
		url: 'v2/getBusiness',
		method: 'POST',
		loading
	})
}