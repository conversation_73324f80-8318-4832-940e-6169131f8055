import request from "@/utils/request.js"

export function Login(data, loading = true) {
	return request({
		url: 'login',
		method: 'POST',
		data,
		loading
	})
}

export function GetImportSignDetail(data, loading = true) {
	return request({
		url: 'getImportSignDetail',
		method: 'POST',
		data,
		loading
	})
}

export function EditTranshiNum(data, loading = true) {
	return request({
		url: 'editTranshiNum',
		method: 'POST',
		data,
		loading
	})
}

export function OutPutOrder(data, loading = true) {
	return request({
		url: 'outPutOrder',
		method: 'POST',
		data,
		loading
	})
}

export function GetImportSignList(data, loading = true) {
	return request({
		url: 'getImportSignList',
		method: 'POST',
		data,
		loading
	})
}