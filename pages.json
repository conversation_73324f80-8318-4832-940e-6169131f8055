{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		/* 客户端-start */
		{
		    "path" : "pages/client/package/package",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "包裹",
		        "enablePullDownRefresh": true
		    }
		    
		},
		{
		    "path" : "pages/client/my/my",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "我的",
		        "enablePullDownRefresh": false
		    }
		    
		},
        {
            "path" : "pages/client/notice/notice",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "消息通知",
                "enablePullDownRefresh": false
            }
            
        },
		{
		    "path" : "pages/client/dkqs/dkqs",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "到库签收",
		        "enablePullDownRefresh": false
		    }
		    
		},
		/* 客户端-end */
		{
		    "path" : "pages/dkqs/dkqs",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "到库签收",
		        "enablePullDownRefresh": false
		    }
		    
		}
		,{
		    "path" : "pages/scan/scan",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "扫描单号",
		        "enablePullDownRefresh": false
		    }
		    
		},{
		    "path" : "pages/hbck/hbck",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "合并出库",
		        "enablePullDownRefresh": true
		    }
		    
		}
		,{
		    "path" : "pages/express/list/list",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "已出库",
				"enablePullDownRefresh": true
		    }
		    
		},{
		    "path" : "pages/express/detail/detail",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "出库详情",
		        "enablePullDownRefresh": false
		    }
		    
		},{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		}
        ,{
		    "path" : "pages/login/login",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "登录",
		        "enablePullDownRefresh": false
		    }
		    
		},{
            "path" : "pages/kdgz/kdgz",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "快递跟踪",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/auth/auth",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/redicret/redicret",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/smck/smck",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "扫码出库",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/smrk/smrk",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "扫码入库",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/yrk/yrk",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "已入库",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/yck/yck",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "已出库",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/my/my",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "我的",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/yck/detail/detail",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/yck/edit/edit",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/business/wrk/wrk",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/client/package/detail/detail",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "包裹详情",
                "enablePullDownRefresh": false
            }
            
        }
    ],
		"tabBar": {
		    "color": "#dbdbdb",
		    "selectedColor": "#fe352e",
		    "borderStyle": "black",
		    "backgroundColor": "#ffffff",
		    "list": [{
		        "pagePath": "pages/client/package/package",
		        "iconPath": "static/tabbar/client/package.png",
		        "selectedIconPath": "static/tabbar/client/package-cur.png",
		        "text": "包裹"
		    },
			// {
			//     "pagePath": "pages/client/dkqs/dkqs",
			//     "iconPath": "static/tabbar/client/dkqs.png",
			//     "selectedIconPath": "static/tabbar/client/dkqs-cur.png",
			//     "text": "到库签收"
			// },
			{
		        "pagePath": "pages/client/my/my",
		        "iconPath": "static/tabbar/client/my.png",
		        "selectedIconPath": "static/tabbar/client/my-cur.png",
		        "text": "我的"
		    }]
		},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "扫描出库",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	}
}
