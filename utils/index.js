const showToast = (option) => {
	const opt = Object.assign({
		mask: true,
		icon: 'none'
	}, option)
	if (option.success) {
		opt.success = () => {
			setTimeout(() => {
				option.success()
			}, option.duration || 1500)
		}
	}
	uni.showToast(opt)
}

const showLoading = (option) => {
	uni.showLoading(Object.assign({
		title: '加载中...',
		mask: true
	}, option))
}

const showModal = (option) => {
	return uni.showModal(Object.assign({
		confirmColor: '#fe352e'
	}, option))
}

const setData = (key, value) => {
	try {
		uni.setStorageSync(key, value)
	} catch (e) {
		console.error(e);
	}
}

const getData = (key) => {
	try {
		return uni.getStorageSync(key)
	} catch (e) {
		return false
	}
}

const removeData = (key) => {
	uni.removeStorageSync(key)
}

const tabbar = ['/pages/client/package/package', '/pages/client/dkqs/dkqs', '/pages/client/my/my']

const toPage = async (url, data, type = '', events) => {
	if (typeof url === 'number') {
		return await uni.navigateBack({
			delta: url
		});
	}
	if (tabbar.includes(url)) {
		type = 'switchTab'
	}
	let str = ''
	let arr = Object.keys(data || {})
	if (data && arr.length > 0) {
		str = '?';
		for (let key in data) {
			let val = data[key];
			if (typeof val === 'object') {
				val = JSON.stringify(val);
			}
			str += `${key}=${val}&`;
		}
		str = str.substring(0, str.length - 1);
	}
	console.log('topage:%s---%s----%s',url, str, type)
	try {
		switch (type) {
			case 'redirectTo':
				await uni.redirectTo({
					url: url + str
				});
				break;
			case 'switchTab':
				await uni.switchTab({
					url
				});
				break;
			case 'reLaunch':
				await uni.reLaunch({
					url: url + str
				});
				break;
			default:
				await uni.navigateTo({
					url: url + str,
					events
				});
		}
	} catch (err) {
	}
}

const parseParam = (data) => {
	const keys = Object.keys(data)
	let str = ''
	for (let i in keys) {
		if (str !== '') {
			str += '&'
		}
		const key = keys[i]
		str += `${key}=${data[key]}`
	}
	return str
}

const doLogin = () => {
	removeData('token')
	showModal({
		content: '请登录后再操作',
		confirmText: '去登录',
		success: res => {
			if (res.confirm) {
				const pages = getCurrentPages()
				const page = pages[pages.length - 1]
				let redirect = ''
				if (page.route !== '/pages/login/login') {
					redirect = `/${page.route}`
					if (page.options) {
						redirect += `?${parseParam(page.options)}`
					}
				}
				toPage('/pages/login/login', {
					redirect: decodeURIComponent(redirect)
				})
			}
		}
	})
}

const checkTime = (i) => {
    return i < 10 ? `0${i}` : i
}

const getTime = () => {
	const today = new Date();
    let y = today.getFullYear();
    let m = today.getMonth() + 1;
    let d = today.getDate();
    let h = today.getHours();
    let i = today.getMinutes();
    let s = today.getSeconds();
    m = checkTime(m);
    d = checkTime(d);
    h = checkTime(h);
    i = checkTime(i);
    s = checkTime(s);
    return `${y}-${m}-${d} ${h}:${i}:${s}`
}

const getSetting = (scope, content) => {
	return new Promise((resolve, reject) => {
		uni.getSetting({
		  success(res) {
		    if (!res.authSetting[scope]) {
		      uni.authorize({
		        scope: scope,
		        success () {
		          resolve()
		        },
				fail () {
					showModal({
						content,
						confirmText: '去设置',
						success: res => {
							reject()
							if (res.confirm) {
								uni.openSetting()
							} else {
								showToast({
									title: '无权限!'
								})
							}
						}
					})
				}
		      })
		    }
			resolve()
		  }
		})
	})
}

export {
	showToast,
	showLoading,
	showModal,
	setData,
	getData,
	removeData,
	toPage,
	parseParam,
	doLogin,
	getTime,
	getSetting
}
