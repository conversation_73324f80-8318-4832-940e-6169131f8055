import {
	showToast,
	showLoading,
	showModal,
	getData,
	setData,
	removeData,
	parseParam,
	toPage,
	doLogin
} from "./index.js"
import store from '@/store/index.js'

const base_url = 'https://wzcxgj.jabiru.ltd/logistics/app/wechatApi/'
// const base_url = 'http://47.98.215.9/logistics/app/wechatApi/'
// const base_url = 'http://47.56.198.205:9081/logisticsTest/app/wechatApi/'

export default async function(option) {
	if (option.loading) {
		showLoading()
	}
	const opt = Object.assign({
		header: {
			'Content-Type': 'application/json; charset=UTF-8',
			'token': getData(store.state.systemType === 'client' ? 'clientToken' : 'token') || ''
		}
	}, option)
	opt.url = base_url + opt.url
	console.log('======== request start ========')
	const [err, res] = await uni.request(opt)
	console.log('======== request end ========')
	if (option.loading) {
		uni.hideLoading()
	}
	if (err) {
		console.error(err, option)
		showModal({
			content: '网络异常，请稍后再试~'
		})
		return Promise.reject()
	} else {
		console.log(res, option)
		const {
			data
		} = res
		if (data.code == 401) {
			if (store.state.systemType === 'client') {
				showToast({
					title: '登录失效,请重新登录'
				})
				store.commit('SET_LOGIN_STATUS', {
					prop: 'isClientLogin',
					value: false
				})
			} else {
				store.commit('SET_LOGIN_STATUS', {
					prop: 'isBusinessLogin',
					value: false
				})
				doLogin()
			}
			return Promise.reject()
		} else if (data.code != 0) {
			showToast({
				title: data.msg || '系统错误!'
			})
			return Promise.reject()
		}
		return Promise.resolve(data)
	}
}
