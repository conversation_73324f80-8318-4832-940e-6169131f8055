import {
	showToast,
	showLoading,
	showModal,
	getData,
	setData,
	removeData,
	parseParam,
	toPage,
	doLogin
} from "./index.js"
import store from '@/store/index.js'

// 统一域名配置
const domain = 'https://api.isd56.net'
// const domain = 'http://47.98.215.9'
// const domain = 'http://47.56.198.205:9081'

const base_url = `${domain}/logistics/app/wechatApi/`

// 导出其他常用的URL配置
export const file_upload_url = `${domain}/logistics/app/file/uploads`
export const web_base_url = domain
export const webUrls = {
	track: `https://www.isd56.net/17track.html`,
	importSign: `https://www.isd56.net/import_sign.html?companyCode=cx`
}

export default async function(option) {
	if (option.loading) {
		showLoading()
	}
	const opt = Object.assign({
		header: {
			'Content-Type': 'application/json; charset=UTF-8',
			'token': getData(store.state.systemType === 'client' ? 'clientToken' : 'token') || ''
		}
	}, option)
	opt.url = base_url + opt.url
	console.log('======== request start ========')
	const [err, res] = await uni.request(opt)
	console.log('======== request end ========')
	if (option.loading) {
		uni.hideLoading()
	}
	if (err) {
		console.error(err, option)
		showModal({
			content: '网络异常，请稍后再试~'
		})
		return Promise.reject()
	} else {
		console.log(res, option)
		const {
			data
		} = res
		if (data.code == 401) {
			if (store.state.systemType === 'client') {
				showToast({
					title: '登录失效,请重新登录'
				})
				store.commit('SET_LOGIN_STATUS', {
					prop: 'isClientLogin',
					value: false
				})
			} else {
				store.commit('SET_LOGIN_STATUS', {
					prop: 'isBusinessLogin',
					value: false
				})
				doLogin()
			}
			return Promise.reject()
		} else if (data.code != 0) {
			showToast({
				title: data.msg || '系统错误!'
			})
			return Promise.reject()
		}
		return Promise.resolve(data)
	}
}

// 统一的文件上传函数
export async function uploadFile(filePath) {
	const [err, res] = await uni.uploadFile({
		url: file_upload_url,
		filePath,
		name: 'files'
	})
	if (err) {
		return Promise.reject(err)
	} else {
		const data = JSON.parse(res.data)
		if (data.code === 0) {
			return Promise.resolve(data.result)
		} else {
			return Promise.reject(data)
		}
	}
}
