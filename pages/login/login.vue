<template>
	<view class="form" v-show="init">
		<view class="form-item">
		    <view class="title">用户名</view>
		    <input v-model="form.username" class="input" placeholder="请输入用户名" />
		</view>
		<view class="form-item">
		    <view class="title">密码</view>
		    <input v-model="form.password" class="input" placeholder="请输入密码" />
		</view>
		<view class="form-item">
		    <button :disabled="!form.username || !form.password" class="btn" type="primary" @click="handleLogin">登录</button>
		</view>
	</view>
</template>

<script>
	import { Login } from '@/api/index.js'
	import { setData, getData, showToast, toPage } from '@/utils/index.js'
	
	export default {
		data() {
			return {
				init: false,
				token: '',
				form: {
					username: '',
					password: ''
				},
				redirect: ''
			};
		},
		onShow() {
			this.token = getData('token')
		},
		onLoad(options) {
			this.redirect = decodeURIComponent(options.redirect || '')
			const token = getData('token')
			if (token) {
				// toPage('pages/express/list/list')
				toPage('/pages/business/smrk/smrk')
				// toPage('/pages/hbck/hbck')
			}
			this.init = true
		},
		methods: {
			async handleLogin() {
				const res = await Login(this.form)
				if (res) {
					setData('token', res.token)
					this.$store.commit('SET_LOGIN_STATUS', {
						prop: 'isBusinessLogin',
						value: true
					})
					showToast({
						title: '登录成功',
						icon: 'success',
						success: () => {
							toPage('/pages/business/smrk/smrk')
						}
					})
				}
			},
			handleNavigate() {
				toPage('/pages/smck/smck')
			},
			handleLogout() {
				setData('token', '')
				this.token = ''
				showToast({
					title: '退出成功'
				})
			}
		}
	}
</script>

<style lang="scss">
	.form {
		padding-bottom: 200rpx;
		height: 100vh;
		background: #fff;
		
		
		.button {
			width: 100%;
			padding-top: 200rpx;
			box-sizing: border-box;
			height: 100vh;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;
		}
	}
</style>
