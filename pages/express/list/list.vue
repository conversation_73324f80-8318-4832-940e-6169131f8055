<template>
	<view class="list">
		<template v-if="list && list.length > 0">
			<view class="list__item" v-for="item in list" :key="item.lid" @click="handleDetail(item.uniCode)">
				<view class="list__item__field">
					<view class="label">出库类型: </view>
					<view class="value">{{ item.outType === 1 ? '普通扫描出库' : item.outType === 2 ? '合并扫描出库' : '未知' }}</view>
				</view>
				<view class="list__item__field">
					<view class="label">出库运单: </view>
					<view class="value">{{ item.trackingNum }}</view>
				</view>
				<view class="list__item__field">
					<view class="label">收件姓名: </view>
					<view class="value">{{ item.putName }}</view>
				</view>
				<view class="list__item__field">
					<view class="label">转运单号: </view>
					<view class="value">{{ item.transhiNum }}</view>
				</view>
				<view class="list__item__field">
					<view class="label">包裹重量: </view>
					<view class="value">{{ item.parcelWeight ? `${item.parcelWeight}KG` : '' }}</view>
				</view>
				<view class="list__item__field">
					<view class="label">出库时间: </view>
					<view class="value">{{ item.outPutTime }}</view>
				</view>
				<view class="list__item__field">
					<view class="label">扫描人员: </view>
					<view class="value">{{ item.optionUserName }}</view>
				</view>
			</view>
		</template>
		<view v-else class="empty">
			暂无数据
		</view>
		<image class="filter" src="../../../static/icon/filter.png" @click="handleFilterPopup" />
		<uni-popup ref="popup" type="bottom">
			<view class="filter-form">
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						出库类型:
					</view>
					<view class="filter-form__item__value">
						<picker @change="handlePickerChange" :value="index" :range="type">
						    <view class="picker">
								{{ index !== '' ? type[index] : '请选择出库类型' }}
						    </view>
						 </picker>
					</view>
				</view>
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						收件姓名:
					</view>
					<view class="filter-form__item__value">
						<input v-model="params.putName" type="text" placeholder="请输入收件姓名" />
					</view>
				</view>
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						出库运单:
					</view>
					<view class="filter-form__item__value">
						<input v-model="params.trackingNum" type="text" placeholder="请输入出库运单" />
					</view>
				</view>
				<view class="filter-form__footer">
					<button type="default" @click="handleReset">重置</button>
					<button class="btn" type="primary" @click="handleSearch">搜索</button>
				</view>
			</view>
		</uni-popup>
		
		<seller-tabbar :type='3' />
	</view>
</template>

<script>
	import { GetImportSignList } from '@/api/index.js'
	import { toPage } from '@/utils/index.js'
	import sellerTabbar from '@/components/Tabbar/sellerTabbar.vue'
	
	export default {
		components: {
			sellerTabbar
		},
		data() {
			return {
				params: {
					limit: 10,
					page: 1,
					outType: 0,
					putName: "",
					trackingNum: ""
				},
				finished: false,
				list: [],
				type: ['普通出库', '合并出库'],
				index: ''
			}
		},
		created() {
			this.init()
		},
		methods: {
			async init() {
				this.params = {
					limit: 10,
					page: 1,
					outType: '',
					putName: "",
					trackingNum: ""
				},
				this.index = ''
				this.list = []
				this.finished = false
				await this.getList()
			},
			async getList() {
				if (this.finished) return
				this.params.outType = this.index !== '' ? Number(this.index) + 1 : ''
				const res = await GetImportSignList(this.params)
				this.params.page++
				this.list.push(...res.list)
				if (this.list.length >= res.count ) {
					this.finished = true
				}
			},
			handleReset() {
				this.init()
				this.handleFilterPopupClose()
			},
			handleSearch() {
				this.params.page = 1
				this.list = []
				this.finished = false
				this.getList()
				this.handleFilterPopupClose()
			},
			handlePickerChange(e) {
				this.index = e.target.value
			},
			handleFilterPopup() {
				this.$refs.popup.open('center')
			},
			handleFilterPopupClose() {
				this.$refs.popup.close()
			},
			handleDetail(uniCode) {
				toPage('/pages/express/detail/detail', { uniCode })
			}
		},
		async onPullDownRefresh() {
			await this.init()
			uni.stopPullDownRefresh()
		},
		onReachBottom() {
			this.getList()
		}
	}
</script>

<style lang="scss">
	page {
		background: #eee;
	}
	.empty {
		padding: 200rpx 0;
		text-align: center;
		font-size: 28rpx;
		color: #999;
	}
	.list {
		padding: 30rpx;
		
		&__item {
			background: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 24rpx;
			padding: 20rpx;
			&:not(:first-child) {
				margin-top: 20rpx;
			}
			
			&__field {
				display: flex;
				
				&:not(:first-child) {
					margin-top: 10rpx;
				}
				
				.label {
					padding-right: 10rpx;
					flex-shrink: 0;
				}
				
				.value {
					word-break: break-all;
				}
			}
		}
	
		.filter {
			position: fixed;
			width: 40rpx;
			height: 40rpx;
			top: 30rpx;
			right: 30rpx;
		}
	}

	.filter-form {
		background: #fff;
		width: 600rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		
		&__item {
			display: flex;
			height: 60rpx;
			align-items: center;
			margin-bottom: 20rpx;
			
			&__label {
				font-size: 28rpx;
				color: #666;
				padding-right: 20rpx;
			}
			
			&__value {
				flex-grow: 1;
				
				input {
					border: 2rpx solid #eee;
					border-radius: 8rpx;
					padding: 0 20rpx;
					height: 60rpx;
					box-sizing: border-box;
					font-size: 28rpx;
				}
			}
		}
		
		&__footer {
			display: flex;
			justify-content: space-between;
			
			button {
				width: 250rpx;
				padding: 0!important;
			}
		}
	}
</style>
