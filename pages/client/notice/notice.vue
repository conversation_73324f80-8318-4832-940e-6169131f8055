<template>
	<view class="notice-list">
		<view class="notice-list__cell">
			<view class="notice-list__cell--label">入库消息推送</view>
			<switch :checked="options.receiveFlag === '1'" @change="handleSwitchChange($event, 'receiveFlag')" />
		</view>
		<view class="notice-list__cell">
			<view class="notice-list__cell--label">发货消息推送</view>
			<switch :checked="options.sendFlag === '1'" @change="handleSwitchChange($event, 'sendFlag')" />
		</view>
	</view>
</template>

<script>
	import {
		showToast,
		showLoading
	} from '@/utils/index.js'
	import {
		UpdateFlag
	} from '@/api/client/user.js'
	
	export default {
		data() {
			return {
				options: {}
			}
		},
		onLoad(options) {
			this.options = options
		},
		methods: {
			async handleSwitchChange(val, type) {
				const { value } = val.target
				this.options[type] = value
				Object.keys(this.options).forEach(key => {
					this.options[key] = Number(this.options[key]) && this.options[key] !== 'null' ? '1' : '0'
				})
				const res = await UpdateFlag(this.options)
				if(res) {
					this.$store.dispatch('getClientUserInfo')
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.notice-list {
	padding-top: 30rpx;
	
	&__cell {
		margin: 30rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		display: flex;
		background: #fff;
		justify-content: space-between;
		align-items: center;
		
		&--label {
			font-size: 28rpx;
			color: #333;
		}
	}
}
</style>
