<template>
	<view class="detail">
		<view class="detail__field">
			<view class="label">出库运单: </view>
			<view class="value">{{ detail.orderNum }}</view>
		</view>
	<!-- 	<view class="detail__field">
			<view class="label">出库类型: </view>
			<view class="value">{{ detail.outType === 1 ? '普通扫描出库' : detail.outType === 2 ? '合并扫描出库' : '未知' }}</view>
		</view> -->
		<view class="detail__field">
			<view class="label">出库运单: </view>
			<view class="value">{{ detail.trackingNum }}</view>
		</view>
		<view class="detail__field">
			<view class="label">收件姓名: </view>
			<view class="value">{{ detail.putName }}</view>
		</view>
		<view class="detail__field">
			<view class="label">转运单号: </view>
			<view class="value">{{ detail.transhiNum }}</view>
		</view>
		<view class="detail__field">
			<view class="label">包裹重量: </view>
			<view class="value">{{ detail.parcelWeight ? `${detail.parcelWeight}KG` : '' }}</view>
		</view>
		<view class="detail__field">
			<view class="label">备注: </view>
			<view class="value">{{ detail.remark }}</view>
		</view>
		<view class="detail__field">
			<view class="label">出库时间: </view>
			<view class="value">{{ detail.outPutTime }}</view>
		</view>
		<view class="detail__field">
			<view class="label">扫描人员: </view>
			<view class="value">{{ detail.optionUserName }}</view>
		</view>
		<view class="detail__field">
			<view class="label">打包照片: </view>
			<view class="value">
				<image v-for="(item, index) in detail.imagesStr" :key="item" :src="item" @click="handlePreview(index)" mode="aspectFit" />
			</view>
		</view>
		
		
		<uni-popup ref="popup" type="center">
			<swiper class="swiper" indicator-dots :current="swiperIndex" @change="handleSwiperChange">
				<swiper-item v-for="item in imgList" :key="item">
					<image class="img" :src="item" mode="aspectFit"></image>
				</swiper-item>
			</swiper>
			<diV class="swiper-btn">
				<button type="primary" class="btn" @click="handleDownload(imgList)">下载全部</button>
				<button type="primary" class="btn" @click="handleDownload(imgList[swiperIndex])">下载当前图片</button>
			</diV>
		</uni-popup>
	</view>
</template>

<script>
	import { OutInfo } from '@/api/business/out.js'
	
	import { showToast, showLoading, getSetting } from '@/utils/index.js'
	export default {
		data() {
			return {
				id: '',
				detail: {},
				imgList: [],
				swiperIndex: 0
			}
		},
		onLoad(options) {
			this.id = options.id
			this.getDetail()
		},
		methods: {
			async getDetail() {
				const { id } = this
				const res = await OutInfo(id)
				if(res && res.jbrOutOrder) {
					res.jbrOutOrder.imagesStr = res.jbrOutOrder.imagesStr ? res.jbrOutOrder.imagesStr.split(',') : []
					this.detail = res.jbrOutOrder
				}
			},
			handlePreview(index) {
				this.imgList = this.detail.imagesStr
				this.$refs.popup.open('center')
				this.swiperIndex = index
			},
			handleSwiperChange(e) {
				this.swiperIndex = e.detail.current
			},
			async handleDownload(url) {
				await getSetting('scope.writePhotosAlbum', '请打开保存到相册的权限')
				showLoading({
					title: '正在下载'
				})
				try{
					if (typeof url === 'string') {
						await this.handleDownloadImage(url)
					} else {
						for (let i = 0; i < url.length; i++) {
							await this.handleDownloadImage(url[i])
						}
					}
					showToast({
						title: '下载成功!'
					})
				}catch(e){
					uni.hideLoading()
					showToast({
						title: '下载失败,请重试'
					})
				}
			},
			handleDownloadImage(url) {
				return new Promise((resolve, reject) => {
					uni.downloadFile({
					    url,
					    success: (res) => {
							const { tempFilePath, statusCode } = res
					        if (statusCode === 200) {
					            uni.saveImageToPhotosAlbum({
					                filePath: tempFilePath,
					                success: function (res) {
					                    resolve()
					                },
									fail: (err) => {
										console.log(err)
									  	reject()
									}
					            })
					        } else {
								reject()
							}
					    },
						fail: (err) => {
							console.log(err)
							reject()
						}
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.swiper {
		width: 750rpx;
		height: 750rpx;
		.img {
			width: 750rpx;
			height: 750rpx;
		}
		
		&-btn {
			display: flex;
			padding: 30rpx;
			justify-content: space-between;
			.btn {
				width: 300rpx;
				padding: 0;
			}
		}
	}
	.detail {
		padding: 30rpx;
		color: #666;
		font-size: 24rpx;
		padding-bottom: 120rpx;
		background: #fff;
		
		&__field {
			display: flex;
			
			&:not(:first-child) {
				margin-top: 10rpx;
			}
			
			.label {
				padding-right: 10rpx;
				flex-shrink: 0;
			}
			
			.value {
				word-break: break-all;
				display: flex;
				flex-wrap: wrap;
				
				image {
					width: 180rpx;
					height: 180rpx;
					background: #000;
					margin: 0 10rpx 10rpx 0;
				}
			}
		}
	}
	.footer {
		width: 690rpx;
		position: fixed;
		bottom: 44rpx;
		left: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		button {
			width: 300rpx;
			padding: 0!important;
		}
	}
</style>
