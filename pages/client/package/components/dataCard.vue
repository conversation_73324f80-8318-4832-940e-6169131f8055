<template>
	<view class="data-card" @click="handleClick">
		<view class="item" v-for="(item, index) in listMap[type]" :key="index">
			<view class="label">
				{{ item.label }}:
			</view>
			<view class="value">
				{{ item.prop === 'putName' ? data['putName'] + `(${data['onlyCode']})` : item.prop === 'remark' ? data['remark'] || '点击查看图片' : data[item.prop] || '-' }}
			</view>
			<view class="copy" v-if="item.copy && data[item.prop]" @click.stop="handleClickCopy(data[item.prop])">复制</view>
		</view>
	</view>
</template>

<script>
	import { showToast } from '@/utils/index.js'
	export default {
		props: {
			data: {
				type: Object,
				default: () => {}
			},
			type: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				listMap: {
					1: [{
						label: '快递单号',
						prop: 'trackingNum',
						copy: true
					},
					{
						label: '收件姓名',
						prop: 'putName'
					},
					{
						label: '入库时间',
						prop: 'enterInfoTime'
					},
					{
						label: '扫描人员',
						prop: 'createUserName'
					}],
					2: [{
						label: '出库单号',
						prop: 'orderNum'
					},
					{
						label: '出库运单',
						prop: 'trackingNum'
					},
					{
						label: '收件姓名',
						prop: 'putName'
					},
					{
						label: '转运单号',
						prop: 'transhiNum',
						copy: true
					},
					{
						label: '包裹重量',
						prop: 'parcelWeight'
					},
					{
						label: '备注',
						prop: 'remark'
					},
					{
						label: '出库时间',
						prop: 'outPutTime'
					},
					{
						label: '扫描人员',
						prop: 'optionUserName'
					}]
				}
			}
		},
		methods: {
			handleClick() {
				this.$emit('click', this.data)
			},
			handleClickCopy(val) {
				uni.setClipboardData({
				    data: val,
				    success: function () {
				        showToast({
							title: '复制成功',
							icon: 'success'
						})
				    }
				});
				console.log('复制成功')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.data-card {
		background: #fff;
		margin: 30rpx;
		padding: 20rpx;
		color: #666;
		font-size: 24rpx;
		border-radius: 20rpx;
		
		.item {
			display: flex;
				
			&:not(:first-child) {
				margin-top: 10rpx;
			}
	
			.label {
				padding-right: 10rpx;
				flex-shrink: 0;
			}		
			.value {
				word-break: break-all;
			}
			.copy {
				flex-shrink: 0;
				padding: 0 10rpx;
				color: #fe352e;
			}
		}
		
	}
</style>
