<template>
	<view class="tab">
		<view class="tab__item" v-for="item in tabs" :key="item.value">
			<text class="tab__item--text" :class="{'cur': index === item.value}" @click="handleTab(item.value)">
				{{ item.label }}
			</text>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			tabIndex: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				index: 1,
				tabs: [{
					label: '已入库',
					count: 0,
					value: 1
				},{
					label: '已发货',
					count: 0,
					value: 2
				}]
			}
		},
		watch: {
			tabIndex: {
				handler: function(val) {
					this.index = val
				},
				immediate: true
			}
		},
		methods: {
			handleTab(index) {
				if (index === this.index) {
					return
				}
				this.index = index
				this.$emit('change', index)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tab {
		display: flex;
		justify-content: center;
		background: #fff;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		
		&__item {
			width: 50%;
			text-align: center;
			
			&--text {
				display: inline-block;
				height: 100rpx;
				line-height: 100rpx;
				position: relative;
				&::after {
					content: '';
					position: absolute;
					width: 0;
					transition: width 0.3s, left 0.3s;
					height: 6rpx;
					background: #fe352e;
					left: 50%;
					bottom: 0;
				}
				&.cur {
					border-width: medium;
					&::after {
						width: 100%;
						left: 0;
					}
				}
			}
		}
	}
</style>
