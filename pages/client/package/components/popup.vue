<template>
	<uni-popup ref="popup" type="center" :mask-click="false">
		<view class="tips">
			<view class="title">唯一身份码</view>
			<view class="id">{{ user.onlyCode }}</view>
			<view class="title">
				当我们代收到您的包裹后，需要通过您的“唯一身份码”将包裹信息推送给您。
				所以务必将您的“唯一身份码”加到代收快递的“收件名字”后面（比如：小明1001）。
			</view>
			
			<icon type="cancel" size="32" @click="handlePopupClose"/>
		</view>
	</uni-popup>
</template>

<script>
	import { getData, setData } from '@/utils/index.js'
	export default {
		data() {
			return {
				hideIdTipsPopup: false
			}
		},
		watch: {
			user: {
				handler: function (val) {
					if (val && val.onlyCode) {
						this.handlePopupShow()
					}
				},
				immediate: true,
				deep: true
			}
		},
		computed: {
			user() {
				return this.$store.state.clientUser
			}
		},
		methods: {
			handlePopupShow() {
				this.showIdTipsPopup = getData('hideIdTipsPopup')
				if (!this.showIdTipsPopup) {
					this.$refs.popup.open()
				}
			},
			handlePopupClose() {
				this.$refs.popup.close()
				setData('hideIdTipsPopup', true)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tips {
		background: #fe352e;
		width: 600rpx;
		padding: 30rpx 20rpx;
		border-radius: 20rpx;
		color: #fff;
		text-align: center;
		position: relative;
		
		.id {
			font-weight: bold;
			font-size: 100rpx;
			padding: 40rpx;
		}
		
		icon {
			position: absolute;
			bottom: -40px;
			left: 50%;
			margin-left: -16px;
		}
	}
</style>
