<template>
	<view class="package">
		<tab :tab-index="tabIndex" @change="handleTabChange" />
		
		<template v-if="isLogin">
			<view class="package__list">
				<view class="empty" v-if="list.length === 0">暂无记录</view>
				<data-card v-else v-for="item in list" :key="item.lid" :data="item" :type="tabIndex" @click="handleClickDetail"></data-card>
			
				<view class="finished" v-if="finished">没有更多了</view>
			</view>

			<view class="package__statistics">小计: {{totalCount}}</view>
		</template>
		
		<template v-else>
			<view v-if="!notFirstLaunch" class="no-login strong" @click="handleLogin">
				点击登录,获取唯一身份ID
			</view>

			<view v-else class="no-login">
				请先登录
			</view>
		</template>

		<tip-popup />
		
		<image class="filter" src="../../../static/icon/filter.png" @click="handleFilterPopup" />
		<uni-popup ref="popup" type="bottom">
			<view class="filter-form">
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						单号信息:
					</view>
					<view class="filter-form__item__value">
						<input v-model="params.trackingNum" type="text" placeholder="出库单号/转运单号/出库运单" />
					</view>
				</view>
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						扫描人员:
					</view>
					<view class="filter-form__item__value">
						<picker @change="handlePickerChange" :value="index" :range="business" range-key="businessUserName">
						    <view class="picker">
								{{ index !== '' ? business[index].businessUserName : '请选择扫描人员' }}
						    </view>
						 </picker>
					</view>
				</view>
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						收件姓名:
					</view>
					<view class="filter-form__item__value">
						<text class="choose-item" :class="{ cur: isNoPutName }" @click="handleChoosePutName('')">全部</text>
						<text class="choose-item" v-for="item in names" :key="item" :class="{ cur: params.putNames.indexOf(item) > -1}" @click="handleChoosePutName(item)">{{ item }}</text>
					</view>
				</view>
				<view class="filter-form__footer">
					<button type="default" @click="handleReset">重置</button>
					<button class="btn" type="primary" @click="handleSearch">搜索</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import TipPopup from './components/popup.vue'
	import Tab from './components/tab.vue'
	import DataCard from './components/dataCard.vue'

	import {
		ListJnIn,
		ListJnOut
	} from '@/api/client/list.js'
	import {
		GetOutPutName,
		GetBusiness
	} from '@/api/client/user.js'
	import {
		showLoading,
		showToast,
		getData,
		toPage
	} from '@/utils/index.js'

	export default {
		components: {
			TipPopup,
			Tab,
			DataCard
		},
		data() {
			return {
				tabIndex: 1,
				params: {
					putNames: [],
					onlyCode: '',
					trackingNum: '',
					businessUserId: '',
					limit: 10,
					page: 1
				},
				tabs: ['已入库', '已发货'],
				totalCount: 0,
				index: '',
				finished: false,
				business: [],
				names: [],
				list: [],
				isFirst: true,
				notFirstLaunch: false
			}
		},
		computed: {
			isLogin() {
				return this.$store.state.isClientLogin
			},
			isNoPutName() {
				return !this.params.putNames.length
			}
		},
		onLoad(options) {
			if (options.tab) {
				this.tabIndex = options.tab
			}
			this.init()
			setTimeout(() => {
				this.isFirst = false
			}, 300)
			this.notFirstLaunch = !!getData('notFirstLaunch')
		},
		onShow() {
			if (!this.isFirst && (!this.list.length)) {
				this.init()
				this.notFirstLaunch = !!getData('notFirstLaunch')
			}
		},
		methods: {
			init() {
				this.$store.commit('SET_SYSTEM_TYPE', 'client')
				if (this.isLogin) {
					this.getNames()
					this.getBusiness()
					this.initList()
				}
			},
			initList() {
				this.params = {
					putNames: [],
					onlyCode: '',
					trackingNum: '',
					businessUserId: '',
					limit: 10,
					page: 1
				}
				this.index = ''
				this.finished = false
				this.list = []
				this.getList()
			},
			handleClickDetail(data) {
				if (this.tabIndex !== 2) {
					return
				}
				toPage('/pages/client/package/detail/detail', { id: data.id })
			},
			handleLogin() {
				uni.getUserProfile({
					desc: '完善用户信息',
					lang: 'zh_CN',
					success: (res) => {
						const {
							avatarUrl,
							nickName
						} = res.userInfo
						uni.login({
							provider: 'weixin',
							success: async (loginRes) => {
								await this.$store.dispatch('clientLogin', {
									code: loginRes.code,
									headImg: avatarUrl,
									wxName: nickName
								})
								this.notFirstLaunch = true
								this.init()
							},
							fail: () => {
								showToast({
									title: '登录失败,请重试'
								})
							}
						});
					},
					fail: () => {
						showToast({
							title: '登录失败,请重试'
						})
					}
				})
			},
			handleFilterPopup() {
				this.$refs.popup.open('center')
			},
			handleFilterPopupClose() {
				this.$refs.popup.close()
			},
			handlePickerChange(e) {
				this.index = e.target.value
			},
			async getNames() {
				const res = await GetOutPutName()
				this.names = res.listPutName
			},
			async getBusiness() {
				const res = await GetBusiness()
				this.business = res.listBusiness
			},
			handleChoosePutName(val) {
				if (!val) {
					this.params.putNames = []
				} else {
					const index = this.params.putNames.indexOf(val)
					if (index === -1) {
						this.params.putNames.push(val)
					} else {
						this.params.putNames.splice(index, 1)
					}
				}
			},
			async getList() {
				if (!this.isLogin || this.finished) {
					return
				}
				const Func = this.tabIndex === 1 ? ListJnIn : ListJnOut
				const params = JSON.parse(JSON.stringify(this.params))
				params.putNames = params.putNames.toString()
				params.businessUserId = this.index ? this.business[this.index].businessUserId : ''
				const res = await Func(params)
				if (res) {
					if (this.params.page === res.page.totalPage) {
						this.finished = true
					}
					this.params.page++
					this.totalCount = res.page.totalCount
					this.list.push(...res.page.list)
				}
			},
			handleTabChange(index) {
				this.tabIndex = index
				this.initList()
			},
			handleReset() {
				this.initList()
				this.handleFilterPopupClose()
			},
			handleSearch() {
				this.params.page = 1
				this.list = []
				this.finished = false
				this.getList()
				this.handleFilterPopupClose()
			}
		},
		async onPullDownRefresh() {
			await this.init()
			uni.stopPullDownRefresh()
		},
		onReachBottom() {
			this.getList()
		}
	}
</script>

<style lang="scss" scoped>
	.no-login.strong {
		background: #fe352e;
		margin: 140rpx auto 0;
		width: 690rpx;
		border-radius: 20rpx;
		color: #fff;
		font-size: 40rpx;
	}
	.package {
		min-height: 100vh;
		display: flex;
		flex-direction: column;

		&__list {
			overflow-y: auto;
			flex-grow: 1;
			padding-bottom: 80rpx;
			padding-top: 100rpx;
			
			.empty, .finished {
				text-align: center;
			}
			
			.empty {
				padding: 100rpx;
			}
			
			.finished {
				padding-bottom: 20rpx;
			}
		}

		&__statistics {
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			flex-shrink: 0;
			color: #fff;
			background: rgba($color: #fe352e, $alpha: 0.6);
			position: fixed;
			bottom: 0;
			width: 100%;
		}
		.filter {
			position: fixed;
			width: 60rpx;
			height: 60rpx;
			top: 20rpx;
			right: 20rpx;
		}
	}
	.filter-form {
		background: #fff;
		width: 600rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		
		&__item {
			display: flex;
			min-height: 60rpx;
			line-height: 60rpx;
			margin-bottom: 20rpx;
			
			&__label {
				font-size: 28rpx;
				color: #666;
				padding-right: 20rpx;
				flex-shrink: 0;
			}
			
			&__value {
				flex-grow: 1;
				max-height: 400rpx;
				overflow-y: auto;
				
				input {
					border: 2rpx solid #eee;
					border-radius: 8rpx;
					padding: 0 20rpx;
					height: 60rpx;
					box-sizing: border-box;
					font-size: 28rpx;
				}
				
				.choose-item {
					display: inline-block;
					padding: 10rpx;
					font-size: 24rpx;
					border: 1px solid #eee;
					color: #999;
					border-radius: 8rpx;
					margin-right: 10rpx;
					margin-bottom: 10rpx;
					line-height: initial;
					
					&.cur {
						background: #fe352e;
						color: #fff;
						border-color: #fe352e;
					}
				}
			}
		}
		
		&__footer {
			display: flex;
			justify-content: space-between;
			
			button {
				width: 250rpx;
				padding: 0!important;
			}
		}
	}
</style>
