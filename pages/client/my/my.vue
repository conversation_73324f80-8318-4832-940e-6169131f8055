<template>
	<view class="user">
		<view class="user__info">
			<view class="end-change" @click="handleSystemChange">切换商家</view>
			<view v-if="isLogin" class="user__info__container">
				<image class="avatar" :src="user.headImg || '../../../static/avatar_default.png'" mode=""></image>
				<view class="info">
					<view class="name">{{ user.wechatName || '' }}</view>
					<view class="id">身份码：{{ user.onlyCode || '' }}</view>
				</view>
			</view>
			<view v-else class="nologin">
				暂未登录,
				<!-- <text @click="handleNavigate('/pages/auth/auth')">去登录</text> -->
				<text @click="handleLogin">立即登录</text>
			</view>
		</view>
		<view class="user__list">
			<view class="user__list__cell" @click="handleNavigate('/pages/client/notice/notice', {
				receiveFlag: user.receiveFlag,
				sendFlag: user.sendFlag
			})">
				<view class="label">消息通知</view>
				<view class="arrow">></view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		toPage,
		showToast,
		showLoading,
		setData
	} from '@/utils/index.js'
	import {
		WxUserLogin,
		GetJnUser
	} from '@/api/client/user.js'
	import { mapState } from 'vuex'
	
	export default {
		data() {
			return {
			}
		},
		computed: {
			...mapState({
				isLogin: 'isClientLogin',
				isBusinessLogin: 'isBusinessLogin',
				user: 'clientUser'
			})
		},
		methods: {
			handleLogin() {
				uni.getUserProfile({
					desc: '完善用户信息',
					lang: 'zh_CN',
					success: (res) => {
						const {
							avatarUrl,
							nickName
						} = res.userInfo
						uni.login({
							provider: 'weixin',
							success: (loginRes) => {
								this.$store.dispatch('clientLogin', {
									code: loginRes.code,
									headImg: avatarUrl,
									wxName: nickName
								})
							},
							fail: () => {
								showToast({
									title: '登录失败,请重试'
								})
							}
						});
					},
					fail: () => {
						showToast({
							title: '登录失败,请重试'
						})
					}
				})
			},
			handleNavigate(path, data, needLogin = true) {
				if (needLogin && !this.isLogin) {
					showToast({
						title: '请先登录'
					})
					return
				}
				toPage(path, data)
			},
			handleSystemChange() {
				this.$store.commit('SET_SYSTEM_TYPE', 'business')
				let page = '/pages/login/login'
				if (this.isBusinessLogin) {
					page = '/pages/business/smrk/smrk'
				}
				toPage(page, null, 'reLaunch')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.user {
		&__info {
			background: #fff;
			position: relative;

			&__container {
				display: flex;
				padding: 80rpx 30rpx;

				.avatar {
					width: 120rpx;
					height: 120rpx;
					border-radius: 120rpx;
					margin-right: 20rpx;
					flex-shrink: 0;
				}

				.info {
					width: 0;
					flex-grow: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.name {
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						font-size: 36rpx;
					}

					.id {
						font-size: 28rpx;
						color: #666;
					}
				}
			}

			.end-change {
				color: #666;
				border: 1rpx solid #999;
				position: absolute;
				padding: 6rpx 20rpx;
				border-radius: 10rpx;
				top: 20rpx;
				right: 20rpx;
				font-size: 24rpx;
			}

			.nologin {
				padding: 100rpx;
				text-align: center;
				font-size: 32rpx;
				color: #666;

				text {
					color: #007AFF;
				}
			}
		}

		&__list {
			padding: 30rpx 0;

			&__cell {
				background: #fff;
				font-size: 32rpx;
				display: flex;
				height: 120rpx;
				padding: 0 20rpx;
				justify-content: space-between;
				align-items: center;

				.label {
					color: #333;
				}

				.arrow {
					color: #999;
				}
			}
		}
	}
</style>
