<template>
	<view class="hbck">
		<view class="form">
			<view class="title">
				<view>快递单号,<text class="count">共({{noCount}})个</text></view>
				<image src="../../static/icon/scan.png" @click="handleNavigate"></image>
			</view>
			<view class="form-item">
				<textarea class="input textarea" type="text" v-model="trackingNum" />
			</view>
			<view class="form-item">
				<input class="input" type="digit" v-model="parcelWeight" placeholder="请输入包裹重量" />
			</view>
			<view class="title">
				打包照片
			</view>
			<view class="form-item">
				<view v-for="(item, index) in imagesStr" :key="item" class="img">
					<image :src="item"></image>
					<image class="close" src="../../static/icon/close.png" @click="handleDelete(index)"></image>
				</view>
				<view class="img add" @click="handleUpload">
					<image src="../../static/icon/upload.png"></image>
				</view>
			</view>
		</view>
		<button type="primary" class="btn" @click="handleSubmit">确认出库</button>
		<seller-tabbar :type='2' />
	</view>
</template>

<script>
	import {
		toPage,
		showToast,
		getData,
		setData,
		showLoading,
		doLogin
	} from '@/utils/index.js'
	import {
		OutPutOrder
	} from '@/api/index.js'
	import { uploadFile } from '@/utils/request.js'
	import sellerTabbar from '@/components/Tabbar/sellerTabbar.vue'
	
	export default {
		components: {
			sellerTabbar
		},
		computed: {
			noCount() {
				const { trackingNum } = this
				if (trackingNum) {
					return trackingNum.split(',').length
				} else {
					return 0
				}
			}
		},
		data() {
			return {
				trackingNum: '',
				parcelWeight: '',
				imagesStr: []
			}
		},
		onLoad(options) {
			const { trackingNum, imagesStr } = options
			if (trackingNum) {
				this.trackingNum = trackingNum
				this.imagesStr = imagesStr ? imagesStr.split(',') : []
			}
		},
		onShow() {
			const trackingNum = getData('multiple-trackingNum')
			if (trackingNum) {
				this.trackingNum = trackingNum
			}
		},
		onPullDownRefresh() {
			uni.stopPullDownRefresh()
			this.handleNavigate()
		},
		methods: {
			handleNavigate() {
				toPage('/pages/scan/scan', {
					multiple: true,
					trackingNum: this.trackingNum
				})
			},
			handleUpload() {
				const self = this
				uni.chooseImage({
					count: 999,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: async (res) => {
						// tempFilePath可以作为img标签的src属性显示图片
						const tempFilePaths = res.tempFilePaths
						showLoading()
						for(let i = 0; i < tempFilePaths.length; i++) {
							try{
								await self.uploadFile(tempFilePaths[i])
							}catch(e){
								showToast({
									title: e.msg || '网络错误!'
								})
								uni.hideLoading()
								return
							}
						}
						uni.hideLoading()
					}
				})
			},
			async uploadFile(filePath) {
				try {
					const result = await uploadFile(filePath)
					this.imagesStr.push(result)
					return Promise.resolve()
				} catch (error) {
					return Promise.reject(error)
				}
			},
			handleDelete(index) {
				this.imagesStr.splice(index, 1)
			},
			handleSubmit() {
				const {
					trackingNum,
					parcelWeight,
					imagesStr
				} = this
				if (!trackingNum) {
					showToast({
						title: '单号不能为空!'
					})
					return
				}
				OutPutOrder({
					trackingNum,
					parcelWeight,
					imagesStr: imagesStr.toString()
				}).then(res => {
					this.trackingNum = ''
					this.imagesStr = []
					if (res) {
						showToast({
							title: '出库成功'
						})
					}
				}).finally(() => {
					setData('multiple-trackingNum', '')
				})
			}
		}
	}
</script>

<style lang="scss">
	.hbck {
		display: flex;
		height: 100vh;
		flex-direction: column;

	}

	.form {
		padding-top: 30rpx;
		align-content: flex-start;
		overflow-y: auto;
		flex-grow: 1;

		&-item {
			flex-wrap: wrap;
		}

		.title {
			display: flex;
			justify-content: space-between;
			width: 100%;
			padding: 0 30rpx;
			margin-bottom: 20rpx;

			image {
				width: 40rpx;
				height: 40rpx
			}
			
			.count {
				color: #fe352e;
			}
		}

		.input.textarea {
			height: 200rpx;
		}

		.img {
			width: 200rpx;
			height: 200rpx;
			margin: 0 20rpx 20rpx 0;
			position: relative;

			image {
				width: 200rpx;
				height: 200rpx;
			}

			.close {
				position: absolute;
				top: -20rpx;
				right: -20rpx;
				width: 40rpx;
				height: 40rpx;
			}

			&.add {
				border: 1px solid #e5e5e5;
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 10rpx;

				image {
					width: 50rpx;
					height: 50rpx;
				}
			}
		}
	}

	.btn {
		width: 600rpx;
		margin: 40rpx auto 0;
		flex-shrink: 0;
	}
</style>
