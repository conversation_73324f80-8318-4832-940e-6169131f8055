<template>
	<view class="user">
		<view class="user__info">
			<view class="end-change" @click="handleSystemChange">切换客户</view>
			<view v-if="isLogin" class="user__info__container">
				<image class="avatar" :src="user.headImg || '../../../static/avatar_default.png'" mode=""></image>
				<view class="info">
					<view class="name">{{ user.nickName || '' }}</view>
					<view class="account">{{ user.username || '' }}</view>
				</view>
			</view>
			<view v-else class="nologin">
				暂未登录,
				<!-- <text @click="handleNavigate('/pages/auth/auth')">去登录</text> -->
				<text @click="handleLogin">立即登录</text>
			</view>
		</view>
		
		<button v-if="isLogin" type="primary" class="btn" @click="handleLogout">退出登录</button>
		
		<business-tabbar :type="5" />
	</view>
</template>

<script>
	import {
		toPage,
		showToast,
		showLoading,
		setData
	} from '@/utils/index.js'
	import { GetBussinessInfo } from '@/api/business/user.js'
	import { mapState } from 'vuex'
	import businessTabbar from '@/components/Tabbar/businessTabbar.vue'
	
	export default {
		components: {
			businessTabbar
		},
		data() {
			return {
				user: {}
			}
		},
		computed: {
			...mapState({
				isLogin: 'isBusinessLogin'
			})
		},
		created() {
			this.getUserInfo()
		},
		methods: {
			async getUserInfo() {
				const res = await GetBussinessInfo()
				this.user = res.user
			},
			handleLogin() {
				toPage('/pages/login/login')
			},
			handleLogout() {
				this.$store.commit('SET_LOGIN_STATUS', {
					prop: 'isBusinessLogin',
					value: false
				})
				setData('token', '')
				showToast({
					title: '退出成功!',
					icon: 'success',
					success: () => {
						this.handleSystemChange()
					}
				})
			},
			handleNavigate(path, data, needLogin = true) {
				if (needLogin && !this.isLogin) {
					showToast({
						title: '请先登录'
					})
					return
				}
				toPage(path, data)
			},
			handleSystemChange() {
				this.$store.commit('SET_SYSTEM_TYPE', 'client')
				this.$store.dispatch('getClientUserInfo')
				toPage('/pages/client/package/package', null, 'reLaunch')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.user {
		.btn {
			position: absolute;
			bottom: 200rpx;
			width: 690rpx;
			left: 30rpx;
		}
		
		&__info {
			background: #fff;
			position: relative;

			&__container {
				display: flex;
				padding: 80rpx 30rpx;

				.avatar {
					width: 120rpx;
					height: 120rpx;
					border-radius: 120rpx;
					margin-right: 20rpx;
					flex-shrink: 0;
				}

				.info {
					width: 0;
					flex-grow: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.name {
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						font-size: 36rpx;
					}

					.account {
						font-size: 32rpx;
						color: #666;
					}
				}
			}

			.end-change {
				color: #666;
				border: 1rpx solid #999;
				position: absolute;
				padding: 6rpx 20rpx;
				border-radius: 10rpx;
				top: 20rpx;
				right: 20rpx;
				font-size: 24rpx;
			}

			.nologin {
				padding: 100rpx;
				text-align: center;
				font-size: 32rpx;
				color: #666;

				text {
					color: #007AFF;
				}
			}
		}

		&__list {
			padding: 30rpx;

			&__cell {
				background: #fff;
				font-size: 28rpx;
				display: flex;
				height: 100rpx;
				padding: 0 20rpx;
				justify-content: space-between;
				align-items: center;

				.label {
					color: #333;
				}

				.arrow {
					color: #999;
				}
			}
		}
	}
</style>
