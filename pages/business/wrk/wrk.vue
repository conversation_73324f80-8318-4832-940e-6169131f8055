<template>
	<view class="wrk">
		<view class="list">
			<view class="item" v-for="(item, index) in like" :key="item.trackingNum">
				<view>{{index+1}}. {{item.trackingNum}} <text v-if="!item.checked">(强制出库)</text></view>
				<view v-for="(subItem, subIndex) in item.inOrderInfo" :key="subItem.trackingNum">
					<switch type="checkbox" :checked="subItem.checked" @change="val => handleChange(val, index, subIndex, subItem)" />
					{{subItem.trackingNum}}   {{subItem.putName}}
				</view>
			</view>
		</view>
		<view class="title">以下单号不存在近似单号，将强制出库</view>
		<view class="list">
			<view class="item" v-for="(item, index) in noLike" :key="item">{{index + 1}}. {{ item }}</view>
		</view>
		<view class="footer">
			<button :loading="loading" class="btn" type="primary" @click="handleSubmit">出库</button>
			<button @click="handleBack">取消</button>
		</view>
	</view>
</template>

<script>
	import {
		GetLikeInOrder,
		OutPutOrder,
		UpdateOut
	} from '@/api/business/out.js'
	import {
		toPage,
		showToast,
		setData
	} from '@/utils/index.js'
	
	export default {
		data() {
			return {
				// 未入库单号
				wrk: [],
				// 已入库单号
				trackingNum: [],
				// 无相近
				noLike: [],
				// 相近订单
				like: [],
				parcelWeight: '',
				imagesStr: '',
				remark: '',
				id: '',
				customer: {},
				eventChannel: '',
				loading: false
			}
		},
		onLoad(options) {
			const { num, trackingNum, imagesStr, parcelWeight, customer, id, remark } = options
			this.wrk = num.split(',')
			this.trackingNum = trackingNum.split(',')
			this.imagesStr = imagesStr
			this.id = id
			this.remark = remark || ''
			this.parcelWeight = parcelWeight
			this.customer = JSON.parse(customer)
			this.getLikeOrder()
			this.eventChannel = this.getOpenerEventChannel()
		},
		methods: {
			handleChange(val, index, subIndex, subItem) {
				subItem.checked = val.detail.value
				this.$set(this.like[index].inOrderInfo, subIndex, subItem)
				const item = this.like[index]
				item.checked = false
				item.inOrderInfo.forEach((element, idx) => {
					if (element.checked) {
						item.checked = true
					}
					if (subItem.checked && idx !== subIndex) {
						element.checked = false
					}
				})
				this.$set(this.like, index, item)
				this.$set(this.like[index], 'inOrderInfo', item.inOrderInfo)
			},
			getLikeOrder() {
				this.wrk.forEach(async item => {
					const res = await GetLikeInOrder({
						trackingNum: item,
						onlyCode: this.customer.onlyCode
					})
					if (res && res.inOrderInfo && res.inOrderInfo.length) {
						res.inOrderInfo.forEach(item => {
							item.checked = false
						})
						this.like.push({
							trackingNum: item,
							checked: false,
							inOrderInfo: res.inOrderInfo,
						})
					} else {
						this.noLike.push(item)
					}
				})
			},
			async handleSubmit() {
				this.loading = true
				const {
					parcelWeight,
					imagesStr,
					noLike,
					like,
					customer,
					trackingNum: _trackingNum,
					id,
					remark,
					eventChannel
				} = this
				const likeNum = []
				like.forEach(item => {
					item.checked = false
					item.inOrderInfo.forEach(subItem => {
						if (subItem.checked) {
							item.checked = true
							likeNum.push(subItem.trackingNum)
						}
					})
					if (!item.checked) {
						likeNum.push(item.trackingNum)
					}
				})
				const trackingNum = [...new Set([...noLike, ...likeNum, ..._trackingNum])]
				if(!trackingNum.length) {
					showToast({
						title: '请选择单号'
					})
					this.loading = false
					return
				}
				if (id) {
					UpdateOut({
						id,
						trackingNum: trackingNum.toString(),
						parcelWeight,
						imagesStr,
						remark
					}).then(res => {
						if (res) {
							eventChannel.emit('callback')
							showToast({
								title: '操作成功',
								success: () => {
									toPage(2)
								}
							})
						}
					}).finally(() => {
						this.loading = false
					})
				} else {
					const listTrackingNum = trackingNum.map(item => {
						return {
							trackingNum: item,
							putName: customer.putName,
							onlyCode: customer.onlyCode
						}
					})
					OutPutOrder({
						parcelWeight,
						imagesStr,
						listTrackingNum,
						remark,
						trackingNum: trackingNum.toString()
					}).then(() => {
						showToast({
							icon: 'success',
							title: '出库成功!',
							success: () => {
								setData('smck-trackingNum', '')
								setData('wrk-clear', '1')
								toPage(1)
							}
						})
					}).finally(() => {
						this.loading = false
					})
				}
			},
			handleBack() {
				setData('wrk-clear', '0')
				toPage(1)
			}
		}
	}
</script>

<style lang="scss">
	.wrk {
		background: #fff;
		min-height: 100vh;
		padding: 30rpx 30rpx 160rpx;
		.title {
			line-height: 60rpx;
		}
		.list {
			.item {
				line-height: 60rpx;
			}
		}
		
		.footer {
			background: #fff;
			width: 690rpx;
			padding-bottom: 44rpx;
			bottom: 0;
			left: 30rpx;
			position: fixed;
			display: flex;
			justify-content: space-between;
			button {
				padding: 0 100rpx;
			}
		}
	}

</style>
