<template>
	<view class="list">
		<template v-if="list && list.length > 0">
			<view class="list__item" v-for="item in list" :key="item.lid">
				<view class="list__item__field">
					<view class="label">快递单号: </view>
					<view class="value">{{ item.trackingNum }}</view>
					<view class="btn" @click="handleClickCopy(item.trackingNum)">复制</view>
				</view>
				<view class="list__item__field">
					<view class="label">收件姓名: </view>
					<view class="value">{{ item.putName }}({{ item.onlyCode }})</view>
				</view>
				<!-- <view class="list__item__field">
					<view class="label">包裹重量: </view>
					<view class="value">{{ item.parcelWeight ? `${item.parcelWeight}KG` : '' }}</view>
				</view> -->
				<view class="list__item__field">
					<view class="label">入库时间: </view>
					<view class="value">{{ item.enterInfoTime }}</view>
				</view>
				<view class="list__item__field">
					<view class="label">扫描人员: </view>
					<view class="value">{{ item.createUserName }}</view>
				</view>
			</view>
			
			<view class="statistics">小计: {{totalCount}}</view>
		</template>
		<view v-else class="empty">
			暂无数据
		</view>
		<image class="filter" src="../../../static/icon/filter.png" @click="handleFilterPopup" />
		<uni-popup ref="popup" type="bottom">
			<view class="filter-form">
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						单号信息:
					</view>
					<view class="filter-form__item__value">
						<input v-model="params.trackingNum" type="text" placeholder="出库单号/转运单号/出库运单" />
					</view>
				</view>
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						唯一身份码:
					</view>
					<view class="filter-form__item__value">
						<input v-model="params.onlyCode" type="text" placeholder="请输入唯一身份码" />
					</view>
				</view>
				<view class="filter-form__item">
					<view class="filter-form__item__label">
						收件姓名:
					</view>
					<view class="filter-form__item__value">
						<input v-model="params.putNames" type="text" placeholder="请输入收件姓名" />
					</view>
				</view>
				<view class="filter-form__footer">
					<button type="default" @click="handleReset">重置</button>
					<button class="btn" type="primary" @click="handleSearch">搜索</button>
				</view>
			</view>
		</uni-popup>
		
		<business-tabbar :type='2' />
	</view>
</template>

<script>
	import { ListIn } from '@/api/business/in.js'
	import { toPage, showToast } from '@/utils/index.js'
	import businessTabbar from '@/components/Tabbar/businessTabbar.vue'
	
	export default {
		components: {
			businessTabbar
		},
		data() {
			return {
				params: {
					limit: 10,
					page: 1,
					onlyCode: "",
					putNames: "",
					trackingNum: "",
					businessUserId: ''
				},
				finished: false,
				list: [],
				index: '',
				totalCount: ''
			}
		},
		created() {
			this.init()
		},
		methods: {
			async init() {
				this.params = {
					limit: 10,
					page: 1,
					onlyCode: "",
					putNames: "",
					trackingNum: "",
					businessUserId: ''
				},
				this.index = ''
				this.list = []
				this.finished = false
				await this.getList()
			},
			async getList() {
				if (this.finished) return
				const res = await ListIn(this.params)
				if (this.params.page >= res.page.totalPage ) {
					this.finished = true
				}
				this.totalCount = res.page.totalCount
				this.params.page++
				this.list.push(...res.page.list)
			},
			handleClickCopy(val) {
				uni.setClipboardData({
				    data: val,
				    success: function () {
				        showToast({
							title: '复制成功',
							icon: 'success'
						})
				    }
				});
				console.log('复制成功')
			},
			handleReset() {
				this.init()
				this.handleFilterPopupClose()
			},
			handleSearch() {
				this.params.page = 1
				this.list = []
				this.finished = false
				this.getList()
				this.handleFilterPopupClose()
			},
			handlePickerChange(e) {
				this.index = e.target.value
			},
			handleFilterPopup() {
				this.$refs.popup.open('center')
			},
			handleFilterPopupClose() {
				this.$refs.popup.close()
			}
		},
		async onPullDownRefresh() {
			await this.init()
			uni.stopPullDownRefresh()
		},
		onReachBottom() {
			this.getList()
		}
	}
</script>

<style lang="scss">
	page {
		background: #eee;
	}
	.empty {
		padding: 200rpx 0;
		text-align: center;
		font-size: 28rpx;
		color: #999;
	}
	.list {
		padding: 30rpx;
		
		&__item {
			background: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 24rpx;
			padding: 20rpx;
			&:not(:first-child) {
				margin-top: 20rpx;
			}
			
			&__field {
				display: flex;
				
				&:not(:first-child) {
					margin-top: 10rpx;
				}
				
				.label {
					padding-right: 10rpx;
					flex-shrink: 0;
				}
				
				.value {
					word-break: break-all;
				}
				
				.btn {
					margin-left: 20rpx;
					color: #fe352e;
					flex-shrink: 0;
				}
			}
		}
	
		.filter {
			position: fixed;
			width: 60rpx;
			height: 60rpx;
			top: 30rpx;
			right: 30rpx;
		}
		
		.statistics {
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			flex-shrink: 0;
			color: #fff;
			background: rgba($color: #fe352e, $alpha: 0.6);
			position: fixed;
			bottom: 120rpx;
			width: 100%;
		}
	}

	.filter-form {
		background: #fff;
		width: 600rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		
		&__item {
			display: flex;
			height: 60rpx;
			align-items: center;
			margin-bottom: 20rpx;
			
			&__label {
				font-size: 28rpx;
				color: #666;
				padding-right: 20rpx;
			}
			
			&__value {
				flex-grow: 1;
				
				input {
					border: 2rpx solid #eee;
					border-radius: 8rpx;
					padding: 0 20rpx;
					height: 60rpx;
					box-sizing: border-box;
					font-size: 28rpx;
				}
			}
		}
		
		&__footer {
			display: flex;
			justify-content: space-between;
			
			button {
				width: 250rpx;
				padding: 0!important;
			}
		}
	}
</style>
