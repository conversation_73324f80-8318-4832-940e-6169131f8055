<template>
	<view class="hbck">
		<view class="form">
			<view class="title">
				<view>快递单号,<text class="count">共({{noCount}})个</text></view>
				<image src="../../../static/icon/scan.png" @click="handleNavigate"></image>
			</view>
			<view class="form-item">
				<textarea class="input textarea" type="text" v-model="trackingNum" />
			</view>
			<view class="form-item">
				<input class="input" type="digit" v-model="parcelWeight" placeholder="请输入包裹重量" />
			</view>
			<view class="title">
				打包照片
			</view>
			<view class="form-item">
				<view v-for="(item, index) in imagesStr" :key="item" class="img">
					<image :src="item"></image>
					<image class="close" src="../../../static/icon/close.png" @click="handleDelete(index)"></image>
				</view>
				<view class="img add" @click="handleUpload">
					<image src="../../../static/icon/upload.png"></image>
				</view>
			</view>
			<view class="title">
				备注
			</view>
			<view class="form-item">
				<textarea class="input textarea" type="text" v-model="remark" />
			</view>
		</view>
		<button :loading="loading" type="primary" class="btn" @click="handleSubmit">确认出库</button>
		<business-tabbar :type='3' />
	</view>
</template>

<script>
	import {
		toPage,
		showToast,
		getData,
		setData,
		showLoading,
		doLogin
	} from '@/utils/index.js'
	import {
		OutPutOrder,
		GetInOrder
	} from '@/api/business/out.js'
	import businessTabbar from '@/components/Tabbar/businessTabbar.vue'
	
	export default {
		components: {
			businessTabbar
		},
		computed: {
			noCount() {
				const { trackingNum } = this
				if (trackingNum) {
					return trackingNum.split(',').length
				} else {
					return 0
				}
			}
		},
		data() {
			return {
				trackingNum: '',
				parcelWeight: '',
				remark: '',
				imagesStr: [],
				listTrackingNum: [],
				loading: false
			}
		},
		onLoad(options) {
			const { trackingNum, imagesStr } = options
			if (trackingNum) {
				this.trackingNum = trackingNum
				this.imagesStr = imagesStr ? imagesStr.split(',') : []
			}
		},
		onShow() {
			const trackingNum = getData('smck-trackingNum') || ''
			if (trackingNum) {
				this.trackingNum = trackingNum
			}
			// 未入库单号出库成功 清空数据
			const wrkClear = getData('wrk-clear')
			if (!!wrkClear && wrkClear !== '0') {
				this.trackingNum = ''
				this.parcelWeight = ''
				this.remark = ''
				this.imagesStr = []
				this.listTrackingNum = []
				setData('wrk-clear', 0)
			}
		},
		onPullDownRefresh() {
			uni.stopPullDownRefresh()
			this.handleNavigate()
		},
		methods: {
			handleNavigate() {
				toPage('/pages/scan/scan', {
					multiple: true,
					trackingNum: this.trackingNum,
					needCheck: true,
					storageKey: 'smck-trackingNum'
				})
			},
			handleUpload() {
				const self = this
				uni.chooseImage({
					count: 999,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: async (res) => {
						// tempFilePath可以作为img标签的src属性显示图片
						const tempFilePaths = res.tempFilePaths
						showLoading()
						for(let i = 0; i < tempFilePaths.length; i++) {
							try{
								await self.uploadFile(tempFilePaths[i])
							}catch(e){
								showToast({
									title: e.msg || '网络错误!'
								})
								uni.hideLoading()
								return
							}
						}
						uni.hideLoading()
					}
				})
			},
			async uploadFile(filePath) {
				const [err, res] = await uni.uploadFile({
					url: 'https://wzcxgj.jabiru.ltd/logistics/app/file/uploads',
					filePath,
					name: 'files'
				})
				if (err) {
					return Promise.reject(err)
				} else {
					const data = JSON.parse(res.data)
					if (data.code === 0) {
						this.imagesStr.push(data.result)
						return Promise.resolve()
					} else {
						return Promise.reject(data)
					}
				}
			},
			handleDelete(index) {
				this.imagesStr.splice(index, 1)
			},
			async handleCheckOrder() {
				this.listTrackingNum = []
				const trackingNum = this.trackingNum.split(',')
				const wrk = []
				for(let i = 0; i<trackingNum.length;i++) {
					const _trackingNum = trackingNum[i]
					const res = await GetInOrder({
						trackingNum: _trackingNum
					})
					if (res && res.inOrderInfo && res.inOrderInfo.onlyCode) {
						this.listTrackingNum.push(res.inOrderInfo)
					} else {
						wrk.push(_trackingNum)
					}
				}
				return wrk
			},
			getMostOnlyCode(list) {
				const map = {}
				list.forEach(item => {
					if (map[item.onlyCode]) {
						map[item.onlyCode]++
					} else {
						map[item.onlyCode]=1
					}
				})
				let count = 0, key = ''
				Object.keys(map).forEach(item => {
					if (map[item] > count) {
						count = map[item]
						key = item
					}
				})
				return list.find(item => item.onlyCode === key)
			},
			async handleSubmit() {
				if (!this.trackingNum) {
					showToast({
						title: '单号不能为空!'
					})
					return
				}
				this.loading = true
				const wrk = await this.handleCheckOrder()

				let {
					parcelWeight,
					imagesStr,
					listTrackingNum,
					remark
				} = this
				remark = remark || '点击查看图片'
				
				const _imagesStr = imagesStr.toString()
				let customer
				if (!listTrackingNum.length) {
					showToast({
						title: '无法确定出库人信息，请先入库'
					})
					
					this.loading = false
					return
				} else {
					customer = this.getMostOnlyCode(listTrackingNum)
				}
				const _trackingNum = listTrackingNum.map(item => {
					item.putName = customer.putName
					item.onlyCode = customer.onlyCode
					return item.trackingNum
				})
				if (wrk.length) {
					showToast({
						title: '存在未入库单号',
						success: () => {
							toPage('/pages/business/wrk/wrk', {
								num: wrk.toString(),
								trackingNum: _trackingNum.toString(),
								customer: JSON.stringify(customer),
								parcelWeight,
								imagesStr: _imagesStr,
								remark
							})
						}
					})
					this.loading = false
					return
				}
				
				OutPutOrder({
					trackingNum: _trackingNum.toString(),
					parcelWeight,
					imagesStr: _imagesStr,
					listTrackingNum,
					remark
				}).then(res => {
					this.trackingNum = ''
					this.parcelWeight = ''
					this.remark = ''
					this.imagesStr = []
					this.listTrackingNum = []
					showToast({
						title: '出库成功'
					})
				}).finally(() => {
					this.loading = false
					setData('smck-trackingNum', '')
				})
			}
		}
	}
</script>

<style lang="scss">
	.hbck {
		display: flex;
		height: 100vh;
		flex-direction: column;
		background: #fff;
	}

	.form {
		padding-top: 30rpx;
		align-content: flex-start;
		overflow-y: auto;
		flex-grow: 1;

		&-item {
			flex-wrap: wrap;
		}

		.title {
			display: flex;
			justify-content: space-between;
			width: 100%;
			padding: 0 30rpx;
			margin-bottom: 20rpx;

			image {
				width: 40rpx;
				height: 40rpx
			}
			
			.count {
				color: #fe352e;
			}
		}

		.input.textarea {
			height: 200rpx;
		}

		.img {
			width: 200rpx;
			height: 200rpx;
			margin: 0 20rpx 20rpx 0;
			position: relative;

			image {
				width: 200rpx;
				height: 200rpx;
			}

			.close {
				position: absolute;
				top: -20rpx;
				right: -20rpx;
				width: 40rpx;
				height: 40rpx;
			}

			&.add {
				border: 1px solid #e5e5e5;
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 10rpx;

				image {
					width: 50rpx;
					height: 50rpx;
				}
			}
		}
	}

	.btn {
		width: 600rpx;
		margin: 40rpx auto 0;
		flex-shrink: 0;
	}
</style>
