<template>
	<view class="detail">
		<view class="detail__field">
			<view class="label">出库运单: </view>
			<view class="value">{{ detail.orderNum }}</view>
		</view>
	<!-- 	<view class="detail__field">
			<view class="label">出库类型: </view>
			<view class="value">{{ detail.outType === 1 ? '普通扫描出库' : detail.outType === 2 ? '合并扫描出库' : '未知' }}</view>
		</view> -->
		<view class="detail__field">
			<view class="label">出库运单: </view>
			<view class="value">{{ detail.trackingNum }}</view>
		</view>
		<view class="detail__field">
			<view class="label">收件姓名: </view>
			<view class="value">{{ detail.putName }}</view>
		</view>
		<view class="detail__field">
			<view class="label">转运单号: </view>
			<view class="value">{{ detail.transhiNum }}</view>
		</view>
		<view class="detail__field">
			<view class="label">包裹重量: </view>
			<view class="value">{{ detail.parcelWeight ? `${detail.parcelWeight}KG` : '' }}</view>
		</view>
		<view class="detail__field">
			<view class="label">备注: </view>
			<view class="value">{{ detail.remark }}</view>
		</view>
		<view class="detail__field">
			<view class="label">出库时间: </view>
			<view class="value">{{ detail.outPutTime }}</view>
		</view>
		<view class="detail__field">
			<view class="label">扫描人员: </view>
			<view class="value">{{ detail.optionUserName }}</view>
		</view>
		<view class="detail__field">
			<view class="label">打包照片: </view>
			<view class="value">
				<image v-for="(item, index) in detail.imagesStr" :key="item" :src="item" @click="handlePreview(index)" mode="aspectFit" />
			</view>
		</view>
		
		<view class="footer">
			<button class="btn" type="primary" @click="handleTran">转运单号</button>
			<button class="btn" type="primary" @click="handleEdit">编辑</button>
		</view>
		
		<uni-popup ref="popup" type="center">
			<view class="filter-form">
				<view class="filter-form__item">
					<view class="filter-form__item__value">
						<input cursor-spacing="100" :focus="focus" v-model="transhiNum" type="text" placeholder="请输入转运单号" />
					</view>
				</view>
				<view class="filter-form__footer">
					<button type="default" @click="handleClose">返回</button>
					<button class="btn" type="primary" @click="handleEnter">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { OutInfo, UpdateOut } from '@/api/business/out.js'
	
	import { showToast, toPage, getData } from '@/utils/index.js'
	export default {
		data() {
			return {
				id: '',
				transhiNum: '',
				detail: {},
				focus: false,
				eventChannel: ''
			}
		},
		onLoad(options) {
			this.id = options.id
			this.eventChannel = this.getOpenerEventChannel()
			this.getDetail()
		},
		methods: {
			async getDetail() {
				const { id } = this
				const res = await OutInfo(id)
				if(res && res.jbrOutOrder) {
					res.jbrOutOrder.imagesStr = res.jbrOutOrder.imagesStr ? res.jbrOutOrder.imagesStr.split(',') : []
					this.detail = res.jbrOutOrder
				}
			},
			handlePreview(current) {
				uni.previewImage({
					current,
				    urls: this.detail.imagesStr,
				})
			},
			handleTran() {
				this.transhiNum = this.detail.transhiNum
				this.focus = false
				this.$refs.popup.open('center')
				this.$nextTick(() => {
					this.focus = true
				})
			},
			handleClose() {
				this.$refs.popup.close()
			},
			handleEdit() {
				const { detail, id, eventChannel } = this
				toPage('/pages/business/yck/edit/edit', {
					id,
					trackingNum: detail.trackingNum,
					parcelWeight: detail.parcelWeight,
					remark: detail.remark || '',
					onlyCode: detail.onlyCode,
					putName: detail.putName,
					imagesStr: detail.imagesStr.toString()
				}, '', {
					freshPage: () => {
						this.getDetail()
						this.getDetail()
						eventChannel && eventChannel.emit('freshPage')
					}
				})
			},
			handleEnter() {
				const { transhiNum, id, eventChannel } = this
				if (!transhiNum) {
					showToast({
						title: '请输入转运单号!'
					})
					return
				}
				UpdateOut({
					transhiNum,
					id
				}).then(res => {
					if (res) {
						showToast({
							title: '操作成功!'
						})
						this.detail.transhiNum = this.transhiNum
						this.$refs.popup.close()
						eventChannel && eventChannel.emit('freshPage')
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.filter-form {
		background: #fff;
		width: 600rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		
		&__item {
			display: flex;
			height: 60rpx;
			padding: 50rpx 0;
			align-items: center;
			margin-bottom: 20rpx;
			
			&__label {
				font-size: 28rpx;
				color: #666;
				padding-right: 20rpx;
			}
			
			&__value {
				flex-grow: 1;
				
				input {
					border: 2rpx solid #eee;
					border-radius: 8rpx;
					padding: 0 20rpx;
					height: 80rpx;
					box-sizing: border-box;
					font-size: 28rpx;
				}
			}
		}
		
		&__footer {
			display: flex;
			justify-content: space-between;
			
			button {
				width: 250rpx;
				padding: 0!important;
			}
		}
	}
	.detail {
		padding: 30rpx;
		color: #666;
		font-size: 24rpx;
		padding-bottom: 120rpx;
		background: #fff;
		
		&__field {
			display: flex;
			
			&:not(:first-child) {
				margin-top: 10rpx;
			}
			
			.label {
				padding-right: 10rpx;
				flex-shrink: 0;
			}
			
			.value {
				word-break: break-all;
				display: flex;
				flex-wrap: wrap;
				
				image {
					width: 180rpx;
					height: 180rpx;
					background: #000;
					margin: 0 10rpx 10rpx 0;
				}
			}
		}
	}
	.footer {
		width: 690rpx;
		position: fixed;
		bottom: 44rpx;
		left: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		button {
			width: 300rpx;
			padding: 0!important;
		}
	}
</style>
