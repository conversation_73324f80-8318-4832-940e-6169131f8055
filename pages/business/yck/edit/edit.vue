<template>
	<view class="hbck">
		<view class="form">
			<view class="title">
				<view>快递单号,<text class="count">共({{noCount}})个</text></view>
				<image src="../../../../static/icon/scan.png" @click="handleNavigate"></image>
			</view>
			<view class="form-item">
				<textarea class="input textarea" type="text" v-model="trackingNum" />
			</view>
			<view class="form-item">
				<input class="input" type="digit" v-model="parcelWeight" placeholder="请输入包裹重量" />
			</view>
			<view class="title">
				备注
			</view>
			<view class="form-item">
				<textarea class="input textarea" v-model="remark" placeholder="请输入备注" />
			</view>
			<view class="title">
				打包照片
			</view>
			<view class="form-item">
				<view v-for="(item, index) in imagesStr" :key="item" class="img">
					<image :src="item"></image>
					<image class="close" src="../../../../static/icon/close.png" @click="handleDelete(index)"></image>
				</view>
				<view class="img add" @click="handleUpload">
					<image src="../../../../static/icon/upload.png"></image>
				</view>
			</view>
		</view>
		<button type="primary" class="btn" @click="handleSubmit">保存修改</button>
	</view>
</template>

<script>
	import {
		toPage,
		showToast,
		getData,
		setData,
		showLoading,
		doLogin
	} from '@/utils/index.js'
	import {
		UpdateOut,
		GetInOrder
	} from '@/api/business/out.js'
	import businessTabbar from '@/components/Tabbar/businessTabbar.vue'

	export default {
		components: {
			businessTabbar
		},
		computed: {
			noCount() {
				const {
					trackingNum
				} = this
				if (trackingNum) {
					return trackingNum.split(',').length
				} else {
					return 0
				}
			}
		},
		data() {
			return {
				trackingNum: '',
				originTrackingNum: '',
				parcelWeight: '',
				remark: '',
				id: '',
				onlyCode: '',
				putName: '',
				imagesStr: [],
				eventChannel: '',
				isFirst: true
			}
		},
		onLoad(options) {
			const {
				trackingNum,
				imagesStr,
				id,
				parcelWeight,
				remark,
				putName,
				onlyCode
			} = options
			this.trackingNum = trackingNum
			this.originTrackingNum = trackingNum || ''
			this.imagesStr = imagesStr ? imagesStr.split(',') : []
			this.id = id
			this.putName = putName
			this.onlyCode = onlyCode
			this.parcelWeight = parcelWeight || ''
			this.remark = remark || ''
			this.eventChannel = this.getOpenerEventChannel()
			setData('smck-edit-trackingNum', trackingNum)
			setTimeout(() => {
				this.isFirst = false
			}, 300)
		},
		onShow() {
			if (!this.isFirst) {
				this.trackingNum = getData('smck-edit-trackingNum') || ''
			}
		},
		onPullDownRefresh() {
			uni.stopPullDownRefresh()
			this.handleNavigate()
		},
		methods: {
			handleNavigate() {
				toPage('/pages/scan/scan', {
					multiple: true,
					trackingNum: this.trackingNum,
					needCheck: true,
					storageKey: 'smck-edit-trackingNum'
				})
			},
			handleUpload() {
				const self = this
				uni.chooseImage({
					count: 999,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: async (res) => {
						// tempFilePath可以作为img标签的src属性显示图片
						const tempFilePaths = res.tempFilePaths
						showLoading()
						for (let i = 0; i < tempFilePaths.length; i++) {
							try {
								await self.uploadFile(tempFilePaths[i])
							} catch (e) {
								showToast({
									title: e.msg || '网络错误!'
								})
								uni.hideLoading()
								return
							}
						}
						uni.hideLoading()
					}
				})
			},
			async uploadFile(filePath) {
				const [err, res] = await uni.uploadFile({
					url: 'https://wzcxgj.jabiru.ltd/logistics/app/file/uploads',
					filePath,
					name: 'files'
				})
				if (err) {
					return Promise.reject(err)
				} else {
					const data = JSON.parse(res.data)
					if (data.code === 0) {
						this.imagesStr.push(data.result)
						return Promise.resolve()
					} else {
						return Promise.reject(data)
					}
				}
			},
			handleDelete(index) {
				this.imagesStr.splice(index, 1)
			},
			async handleCheckOrder() {
				const trackingNum = this.trackingNum.split(',')
				const wrk = [],
					hasSignOrder = []
				for (let i = 0; i < trackingNum.length; i++) {
					const _trackingNum = trackingNum[i]
					const res = await GetInOrder({
						trackingNum: _trackingNum
					})
					if (res && res.inOrderInfo && res.inOrderInfo.onlyCode) {
						hasSignOrder.push(_trackingNum)
					} else {
						wrk.push(_trackingNum)
					}
				}
				return {
					wrk,
					hasSignOrder
				}
			},
			async handleSubmit() {
				const {
					id,
					trackingNum,
					originTrackingNum,
					parcelWeight,
					remark,
					imagesStr,
					eventChannel,
					onlyCode,
					putName
				} = this
				if (!trackingNum) {
					showToast({
						title: '单号不能为空!'
					})
					return
				}
				if (trackingNum !== originTrackingNum) {
					const {
						wrk,
						hasSignOrder
					} = await this.handleCheckOrder()
					if (wrk.length) {
						showToast({
							title: '存在未入库单号',
							success: () => {
								toPage('/pages/business/wrk/wrk', {
									num: wrk.toString(),
									trackingNum: hasSignOrder.toString(),
									customer: JSON.stringify({
										onlyCode,
										putName
									}),
									parcelWeight,
									remark,
									imagesStr: imagesStr.toString(),
									id
								}, '', {
									callback: () => {
										setData('smck-edit-trackingNum', '')
										eventChannel.emit('freshPage')
									}
								})
							}
						})
						return
					}
				}
				UpdateOut({
					id,
					trackingNum,
					parcelWeight,
					remark,
					imagesStr: imagesStr.toString()
				}).then(res => {
					if (res) {
						this.trackingNum = ''
						this.imagesStr = []
						eventChannel.emit('freshPage')
						showToast({
							title: '操作成功',
							success: () => {
								toPage(1)
							}
						})
						setData('smck-edit-trackingNum', '')
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.hbck {
		display: flex;
		height: 100vh;
		flex-direction: column;
		background: #fff;
	}

	.form {
		padding-top: 30rpx;
		align-content: flex-start;
		overflow-y: auto;
		flex-grow: 1;

		&-item {
			flex-wrap: wrap;
		}

		.title {
			display: flex;
			justify-content: space-between;
			width: 100%;
			padding: 0 30rpx;
			margin-bottom: 20rpx;

			image {
				width: 40rpx;
				height: 40rpx
			}

			.count {
				color: #fe352e;
			}
		}

		.input.textarea {
			height: 200rpx;
		}

		.img {
			width: 200rpx;
			height: 200rpx;
			margin: 0 20rpx 20rpx 0;
			position: relative;

			image {
				width: 200rpx;
				height: 200rpx;
			}

			.close {
				position: absolute;
				top: -20rpx;
				right: -20rpx;
				width: 40rpx;
				height: 40rpx;
			}

			&.add {
				border: 1px solid #e5e5e5;
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 10rpx;

				image {
					width: 50rpx;
					height: 50rpx;
				}
			}
		}
	}

	.btn {
		width: 600rpx;
		margin: 40rpx auto 44rpx;
		flex-shrink: 0;
	}
</style>
