<template>
	<view class="smrk">
		<view class="form">
			<view class="title">
				<view>入库单号</view>
				<image src="../../../static/icon/scan.png" @click="handleNavigate"></image>
			</view>
			<view class="form-item">
				<textarea class="input textarea" type="text" v-model="trackingNum" />
			</view>
			<view class="title">
				唯一身份码
			</view>
			<view class="form-item">
				<input class="input" v-model="onlyCode" placeholder="请输入唯一身份码" @blur="getPutNameByOnlyCode" />
			</view>
			<view class="title">
				姓名
				<view><switch type="checkbox" :checked="addFlag === 1" @change="handleAddFlagChange" />新增</view>
			</view>
			<view class="form-item">
				<input class="input" v-model="putName" placeholder="请输入姓名" @blur="getOnlyCodeByName" />
			</view>
			<view class="title" v-if="names.length">
				唯一码关联姓名
			</view>
			<view class="names">
				<view v-for="item in names" :key="item" @click="handleSetPutName(item)">{{item}}</view>
			</view>
			<view class="title" v-if="historyNames.length">
				历史姓名
			</view>
			<view class="names">
				<view v-for="item in historyNames" :key="item" @click="handleSetPutName(item, true)">{{item}}</view>
			</view>
		</view>
		<button :loading="loading" type="primary" class="btn" @click="handleSubmit">确认入库</button>
		<business-tabbar :type='1' />
	</view>
</template>

<script>
	import {
		toPage,
		showToast,
		getData,
		setData,
		getTime
	} from '@/utils/index.js'
	import { 
		EntryScanning,
		GetPutName,
		GetOnlyCodeByName
	} from '@/api/business/in.js'
	import businessTabbar from '@/components/Tabbar/businessTabbar.vue'
	
	export default {
		components: {
			businessTabbar
		},
		computed: {
			noCount() {
				const { trackingNum } = this
				if (trackingNum) {
					return trackingNum.split(',').length
				} else {
					return 0
				}
			}
		},
		data() {
			return {
				trackingNum: '',
				onlyCode: '',
				putName: '',
				enterInfoTime: '',
				addFlag: 0,
				names: [],
				historyNames: [],
				loading: false
			}
		},
		onLoad(options) {
			const { trackingNum, imagesStr } = options
			if (trackingNum) {
				this.trackingNum = trackingNum
				this.imagesStr = imagesStr ? imagesStr.split(',') : []
			}
			this.getHistoryPutName()
		},
		onShow() {
			const trackingNum = getData('smrk-trackingNum')
			if (trackingNum) {
				this.trackingNum = trackingNum
			}
		},
		onPullDownRefresh() {
			uni.stopPullDownRefresh()
			this.handleNavigate()
		},
		methods: {
			handleNavigate() {
				toPage('/pages/scan/scan', {
					multiple: true,
					trackingNum: this.trackingNum,
					storageKey: 'smrk-trackingNum'
				})
			},
			async getPutNameByOnlyCode() {
				if (this.onlyCode) {
					const res = await GetPutName({
						onlyCode: this.onlyCode
					})
					this.names = res ? res.list : []
				}
			},
			async getHistoryPutName() {
				const res = await GetPutName({
					onlyCode: ''
				})
				this.historyNames = res ? res.list : []
			},
			async getOnlyCodeByName() {
				if (this.putName) {
					const res = await GetOnlyCodeByName({
						keywork: this.putName
					})
					if (res && res.list.length && res.list[0].putName === this.putName) {
						this.onlyCode = res.list[0].onlyCode
						this.addFlag = 0
					} else {
						this.addFlag = 1
					}
				}
			},
			handleSetPutName(val, getOnlyCode) {
				this.putName = val
				if (getOnlyCode) {
					this.getOnlyCodeByName()
				} else {
					this.addFlag = 0
				}
			},
			handleAddFlagChange(val) {
				const { value } = val.detail
				this.addFlag = value ? 1 : 0
			},
			handleSubmit() {
				const {
					trackingNum,
					onlyCode,
					putName,
					addFlag
				} = this
				if (!trackingNum) {
					showToast({
						title: '单号不能为空!'
					})
					return
				}
				if (!onlyCode) {
					showToast({
						title: '唯一身份码不能为空!'
					})
					return
				}
				if (!putName) {
					showToast({
						title: '姓名不能为空!'
					})
					return
				}
				this.loading = true
				EntryScanning({
					trackingNum,
					onlyCode,
					putName,
					addFlag,
					enterInfoTime: getTime()
				}).then(res => {
					if (res) {
						this.trackingNum = ''
						this.onlyCode = ''
						this.putName = ''
						this.enterInfoTime = ''
						this.names = []
						this.addFlag = 0
						this.imagesStr = []
						showToast({
							title: '入库成功',
							success: () => {
								this.getHistoryPutName()
							}
						})
						
					} else {
						showToast({
							title: res.msg
						})
					}
				}).finally(() => {
					this.loading = false
					setData('smrk-trackingNum', '')
				})
			}
		}
	}
</script>

<style lang="scss">
	.smrk {
		display: flex;
		height: 100vh;
		flex-direction: column;
		background: #fff;
	}

	.form {
		padding-top: 30rpx;
		align-content: flex-start;
		overflow-y: auto;
		flex-grow: 1;

		&-item {
			flex-wrap: wrap;
		}
		
		.names {
			padding: 0 30rpx;
			view {
				display: inline-block;
				padding: 5rpx 20rpx;
				border: 2rpx solid #007AFF;
				border-radius: 40rpx;
				color: #007AFF;
				margin-right: 10rpx;
				margin-bottom: 20rpx;
				font-size: 28rpx;
			}
		}

		.title {
			display: flex;
			justify-content: space-between;
			width: 100%;
			padding: 0 30rpx;
			margin-bottom: 20rpx;

			image {
				width: 40rpx;
				height: 40rpx
			}
			
			.count {
				color: #fe352e;
			}
		}

		.input.textarea {
			height: 200rpx;
		}

		.img {
			width: 200rpx;
			height: 200rpx;
			margin: 0 20rpx 20rpx 0;
			position: relative;

			image {
				width: 200rpx;
				height: 200rpx;
			}

			.close {
				position: absolute;
				top: -20rpx;
				right: -20rpx;
				width: 40rpx;
				height: 40rpx;
			}

			&.add {
				border: 1px solid #e5e5e5;
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 10rpx;

				image {
					width: 50rpx;
					height: 50rpx;
				}
			}
		}
	}

	.btn {
		width: 600rpx;
		margin: 40rpx auto 0;
		flex-shrink: 0;
	}
</style>
