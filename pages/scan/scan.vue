<template>
	<!-- <view class="scan-code">
		<camera class='scan-camera' mode="scanCode" @error="cameraError" @scancode='scancode' frame-size='large'>
		</camera>
		
		<cover-view v-if="multiple" class="cover" >
			<cover-view class="cover-list">
				<cover-view class="cover-list__item" v-for="(item, index) in list" :key="item">
					{{ item }}
					<cover-view class="del" @click="handleDelete(index)">删除</cover-view>
				</cover-view>
			</cover-view>
			<cover-view class="cover-btn" @click="handleBack(true)">确定</cover-view>
		</cover-view>
		
	</view> -->
	<view v-if="multiple" class="cover">
		<view class="cover-list">
			<view class="cover-list__item" v-for="(item, index) in list" :key="list[listLength - index]">
				<view>
					{{ list[listLength - index] }}
					<text v-if="needCheck" :class="{ red: trackingNumMap[list[listLength - index]] && trackingNumMap[list[listLength - index]].putName !== name }">({{ trackingNumMap[list[listLength - index]] && trackingNumMap[list[listLength - index]].putName }})</text>
				</view>
				
				<view class="del" @click="handleDelete((listLength - index))">删除</view>
			</view>
		</view>
		<view class="cover-btn">
			<button type="primary" class="btn" @click="handleBack(true)">
				确定
				<template v-if="multiple">
					({{list.length}})
				</template>
			</button>
			<button type="primary" class="btn" @click="handleScan">连续扫码</button>
		</view>
		
	</view>
</template>

<script>
	import {
		parseParam,
		setData,
		showToast
	} from '@/utils/index.js'
	import {
		GetInOrder
	} from '@/api/business/out.js'
	
	
	let animation = uni.createAnimation({});
	let ani_t;
	
	export default {
		data() {
			return {
				flash: false,
				animation: {},
				result: [],
				list: [],
				storageKey: 'smck-trackingNum',
				multiple: false,
				// 远程校验单号
				needCheck: false,
				trackingNumMap: {},
				nameMap: {}
			};
		},
		computed: {
			name() {
				let count = 0, name = ''
				Object.keys(this.nameMap).forEach(key => {
					if (this.nameMap[key].count > count) {
						count = this.nameMap[key].count
						name = key
					}
				})
				return name
			},
			listLength() {
				return this.list.length - 1
			}
		},
		onLoad(options) {
			const { trackingNum, multiple, needCheck, storageKey } = options
			this.multiple = !!multiple
			this.needCheck = !!needCheck
			this.storageKey = storageKey
			if (trackingNum) {
				this.list = trackingNum.split(',')
				this.list.forEach(item => {
					this.getInOrder(item, false)
				})
			}
			this.handleScan()
		},
		onUnload() {
			clearInterval(ani_t);
		},
		methods: {
			// 扫码成功
			async scancode(e) {
				this.handleScanResult(e.detail.result)
			},

			// 调用扫一扫
			async handleScan() {
				uni.scanCode({
				    scanType: ['barCode'],
				    success: async (res) => {
						const { result } = res
						if (this.list.includes(result)) {
							setTimeout(() => {
								showToast({
									title: '单号已存在'
								})
							}, 300)
						} else{
							this.list.push(result)
							await this.getInOrder(result)
							this.handleBack()
						}
						setTimeout(() => {
							uni.hideToast()
							this.handleScan()
						}, 1000)
				    }
				});
			},
			async getInOrder(trackingNum, loading = true) {
				if (this.needCheck) {
					const res = await GetInOrder({
						trackingNum
					}, loading)
					if (res && res.inOrderInfo) {
						const { trackingNum, putName } = res.inOrderInfo
						this.$set(this.trackingNumMap, trackingNum, res.inOrderInfo)
						if (!this.nameMap[putName]) {
							this.$set(this.nameMap, putName, { count: 1 })
						} else {
							this.nameMap[putName].count++
						}
					}
				}
			},
			async handleScanResult(num) {
				if (this.result.includes(num) || this.list.includes(num)) {
					return
				} else {
					this.result.push(num)
					const innerAudioContext = uni.createInnerAudioContext();//新建一个createInnerAudioContext();
					innerAudioContext.autoplay = true;//音频自动播放设置
					innerAudioContext.src = 'https://img.tukuppt.com/newpreview_music/00/10/98/5d819f842129d49542.mp3';//链接到音频的地址
					innerAudioContext.onPlay(() => {});//播放音效
					innerAudioContext.onEnded((res) => {
						this.list.push(num)
						this.handleBack()
						this.result = []
						innerAudioContext.destroy()
					})
				}
			},
			// 打开闪光灯
			handleOpenFlash() {
				this.flash = !this.flash;
			},
			handleDelete(index) {
				this.list.splice(index, 1)
			},
			handleBack(back) {
				setData(this.storageKey, this.list.toString())
				if (!this.multiple || back) {
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style lang="scss">
	.cover {
		padding: 30rpx 30rpx 0;
		width: 100%;
		box-sizing: border-box;
		height: 100vh;
		display: flex;
		justify-content: center;
		flex-direction: column;
		color: #333;
		
		&-list {
			overflow-y: auto;
			flex-grow: 1;
			
			&__item {
				word-break: break-all;
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-shrink: 0;
				
				.red {
					color: #fe352e;
				}
				
				.del {
					margin-left: 30rpx;
					padding: 10rpx 0;
					flex-shrink: 0;
				}
			}
		}
		
		&-btn {
			display: flex;
			padding: 30rpx 0;
			width: 100%;
			flex-shrink: 0;
			text-align: center;
			justify-content: space-between;
			.btn {
				width: 320rpx;
				padding: 0!important;
			}
		}
	}
	.scan-code {
		.scan-camera {
			width: 100vw;
			height: 100vh;
		}
	}
	
</style>
