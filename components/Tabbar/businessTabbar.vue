<template>
	<view class="tabbar">
		<view class="tabbar-container">
			<view class="tabbar-container__item" :class="{'cur': type === 1}" @click="handleNavigate(1)">
				<image v-if="type === 1" src="../../static/tabbar/business/smrk-cur.png"></image>
				<image v-else src="../../static/tabbar/business/smrk.png"></image>
				<text>扫码入库</text>
			</view>
			<view class="tabbar-container__item" :class="{'cur': type === 2}" @click="handleNavigate(2)">
				<image v-if="type === 2" src="../../static/tabbar/business/yrk-cur.png"></image>
				<image v-else src="../../static/tabbar/business/yrk.png"></image>
				<text>已入库</text>
			</view>
			<view class="tabbar-container__item" :class="{'cur': type === 3}" @click="handleNavigate(3)">
				<image v-if="type === 3" src="../../static/tabbar/business/smck-cur.png"></image>
				<image v-else src="../../static/tabbar/business/smck.png"></image>
				<text>扫码出库</text>
			</view>
			<view class="tabbar-container__item" :class="{'cur': type === 4}" @click="handleNavigate(4)">
				<image v-if="type === 4" src="../../static/tabbar/business/yck-cur.png"></image>
				<image v-else src="../../static/tabbar/business/yck.png"></image>
				<text>已出库</text>
			</view>
			<view class="tabbar-container__item" :class="{'cur': type === 5}" @click="handleNavigate(5)">
				<image v-if="type === 5" src="../../static/tabbar/business/my-cur.png"></image>
				<image v-else src="../../static/tabbar/business/my.png"></image>
				<text>我的</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { toPage } from '@/utils/index.js'
	export default {
		name:"Tabbar",
		props: {
			type: {
				type: Number,
				default: 1
			}
		},
		methods:{
			handleNavigate(type) {
				if (type === this.type) return
				let url = ''
				switch (type){
					case 1:
						url = '/pages/business/smrk/smrk'
						break;
					case 2:
						url = '/pages/business/yrk/yrk'
						break;
					case 3:
						url = '/pages/business/smck/smck'
						break;
					case 4:
						url = '/pages/business/yck/yck'
						break;
					case 5:
						url = '/pages/business/my/my'
						break;
					default:
						url = '/pages/business/smrk/smrk'
						break;
				}
				toPage(url, '', 'redirectTo')
			}
		}
	}
</script>

<style lang="scss">
	.tabbar {
		margin-top: 30rpx;
		height: 120rpx;
		flex-shrink: 0;
		
		&-container {
			position: fixed;
			width: 100%;
			bottom: 0;
			left: 0;
			background-color: #fff;
			display: flex;
			border-top: 1px solid #dbdbdb;
			
			&__item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
				color: #dbdbdb;
				flex-grow: 1;
				height: 120rpx;
				
				image {
					width: 48rpx;
					height: 48rpx;
					margin-bottom: 10rpx;
				}
				&.cur {
					color: #fe352e;
				}
			}
		}
	}

</style>
