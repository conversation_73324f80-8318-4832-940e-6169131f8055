<template>
	<view class="tabbar">
		<view class="tabbar-container">
			<view class="tabbar-container__item" :class="{'cur': type === 1}" @click="handleNavigate(1)">
				<image v-if="type === 1" src="../../static/tabbar/client/package-cur.png"></image>
				<image v-else src="../../static/tabbar/client/package.png"></image>
				<text>包裹</text>
			</view>
			<view class="tabbar-container__item" :class="{'cur': type === 2}" @click="handleNavigate(2)">
				<image v-if="type === 2" src="../../static/tabbar/client/my-cur.png"></image>
				<image v-else src="../../static/tabbar/client/my.png"></image>
				<text>我的</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { toPage } from '@/utils/index.js'
	export default {
		name:"Tabbar",
		props: {
			type: {
				type: Number,
				default: 1
			}
		},
		methods:{
			handleNavigate(type) {
				if (type === this.type) return
				let url = ''
				switch (type){
					case 1:
						url = '/pages/client/package/package'
						break;
					case 2:
						url = '/pages/client/my/my'
						break;
					default:
						url = '/pages/client/package/package'
						break;
				}
				toPage(url, '', 'redirectTo')
			}
		}
	}
</script>

<style lang="scss">
	.tabbar {
		margin-top: 30rpx;
		height: 120rpx;
		flex-shrink: 0;
		
		&-container {
			position: fixed;
			width: 100%;
			bottom: 0;
			left: 0;
			background-color: #fff;
			display: flex;
			border-top: 1px solid #dbdbdb;
			
			&__item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
				color: #dbdbdb;
				flex-grow: 1;
				height: 120rpx;
				
				image {
					width: 48rpx;
					height: 48rpx;
					margin-bottom: 10rpx;
				}
				&.cur {
					color: #fe352e;
				}
			}
		}
	}

</style>
