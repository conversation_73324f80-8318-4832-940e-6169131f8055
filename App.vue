<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			this.$store.dispatch('getClientUserInfo')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		created() {
			console.log(this.$store.state)
		}
	}
</script>

<style lang="scss">
	page {
		background: #eee;
	}
	view {
		box-sizing: border-box;
	}
	button.btn {
		&.btn {
			padding: 0 100rpx;
			background-color: #fe352e;
			
			&[disabled] {
				background: #f7f7f7;
				color: #666;
			}
		}
	}
	/*每个页面公共css */
	.form {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-wrap: wrap;
		align-content: center;
		
		&-item {
			display: flex;
			width: 100%;
			line-height: 80rpx;
			padding: 0 30rpx;
			margin-bottom: 40rpx;
			
			.title {
				font-size: 28rpx;
				color: #666;
				width: 100rpx;
				flex-shrink: 0;
			}
			
			.input {
				border: 2rpx solid #e5e5e5;
				border-radius: 8rpx;
				height: 60rpx;
				padding: 10rpx 20rpx;
				flex: 1;
			}
		}
	}
	
	.no-login {
		height: 500rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
