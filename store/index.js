import { setData, getData, toPage } from '@/utils/index.js'
import { WxUserLogin, GetJnUser } from '@/api/client/user.js'

// 客户端token
const clientToken = 'clientToken'
// 商家端token
const businessToken = 'token'

// #ifndef VUE3
import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
const store = new Vuex.Store({
// #endif

// #ifdef VUE3
import { createStore } from 'vuex'
const store = createStore({
// #endif
	state: {
		systemType: 'client', // client: 客户端, business: 商家端
		onlyCode: '', // 用户唯一码
		isClientLogin: !!getData(clientToken), // 客户端是否登录
		isBusinessLogin: !!getData(businessToken), // 商家端是否登录
		clientUser: {
			
		},
		businessUser: {
			
		}
	},
	mutations: {
		SET_SYSTEM_TYPE(state, type) {
			state.systemType = type
		},
		SET_LOGIN_STATUS(state, payload) {
			state[payload.prop] = payload.value
		},
		SET_ONLY_CODE(state, code) {
			state.onlyCode = code
		},
		SET_USER_INFO(state, payload) {
			state[payload.prop] = payload.value
		}
	},
	getters: {
		
	},
	actions: {
		async clientLogin({ commit, dispatch }, payload) {
			const res = await WxUserLogin(payload)
			const { onlyCode, token, firstLoginCode } = res
			setData(clientToken, token)
			commit('SET_LOGIN_STATUS', {
				prop: 'isClientLogin',
				value: true
			})
			commit('SET_ONLY_CODE', onlyCode)
			await dispatch('getClientUserInfo')
			setData('notFirstLaunch', true)
			if (firstLoginCode) {
				toPage('/pages/client/package/package')
			}
		},
		async getClientUserInfo({ commit, state }) {
			if (!getData(clientToken)) {
				return
			}
			if (state.systemType !== 'client') {
				return
			}
			const res = await GetJnUser()
			commit('SET_USER_INFO', {
				prop: 'clientUser',
				value: res.jnUser
			})
		}
	}
})

export default store
